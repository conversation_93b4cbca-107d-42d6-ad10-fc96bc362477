"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { 
  Users, 
  Plus, 
  Search, 
  MoreHorizontal, 
  Edit, 
  Trash2, 
  Eye,
  ExternalLink,
  Music,
  Instagram,
  Twitter,
  Globe,
  User
} from "lucide-react"
import { artistService } from "@/lib/database"
import type { Artist } from "@/lib/types"
import { toast } from "sonner"

export default function ArtistsPage() {
  const [artists, setArtists] = useState<Artist[]>([])
  const [filteredArtists, setFilteredArtists] = useState<Artist[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedArtist, setSelectedArtist] = useState<Artist | null>(null)
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false)
  const [viewMode, setViewMode] = useState<"table" | "cards">("cards")

  useEffect(() => {
    loadArtists()
  }, [])

  useEffect(() => {
    // Filter artists based on search term
    const filtered = artists.filter(artist =>
      artist.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      artist.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (artist.bio && artist.bio.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    setFilteredArtists(filtered)
  }, [artists, searchTerm])

  const loadArtists = async () => {
    try {
      setIsLoading(true)
      const data = await artistService.getAll()
      setArtists(data)
    } catch (error) {
      console.error("Error loading artists:", error)
      toast.error("Failed to load artists")
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteArtist = async (artistId: string) => {
    if (!confirm("Are you sure you want to delete this artist?")) return
    
    try {
      await artistService.delete(artistId)
      toast.success("Artist deleted successfully")
      loadArtists()
    } catch (error) {
      console.error("Error deleting artist:", error)
      toast.error("Failed to delete artist")
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getSocialLinks = (artist: Artist) => {
    const links = []
    if (artist.instagram) links.push({ type: 'instagram', url: artist.instagram, icon: Instagram })
    if (artist.twitter) links.push({ type: 'twitter', url: artist.twitter, icon: Twitter })
    if (artist.website) links.push({ type: 'website', url: artist.website, icon: Globe })
    return links
  }

  // Calculate stats
  const totalArtists = artists.length
  const artistsWithPhotos = artists.filter(a => a.image_url).length
  const artistsWithSocial = artists.filter(a => a.instagram || a.twitter || a.website).length

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader className="pb-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Artists Management</h1>
          <p className="text-gray-600">Manage your performers and artists</p>
        </div>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button className="bg-amber-600 hover:bg-amber-700">
              <Plus className="w-4 h-4 mr-2" />
              Add Artist
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Add New Artist</DialogTitle>
              <DialogDescription>
                Add a new performer to your Wine & Dine events
              </DialogDescription>
            </DialogHeader>
            <div className="p-4">
              <p className="text-sm text-gray-500">Artist creation form will be implemented here</p>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Artists</CardTitle>
            <Music className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalArtists}</div>
            <p className="text-xs text-muted-foreground">
              Registered performers
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Photos</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{artistsWithPhotos}</div>
            <p className="text-xs text-muted-foreground">
              Have profile photos
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">With Social Media</CardTitle>
            <Instagram className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{artistsWithSocial}</div>
            <p className="text-xs text-muted-foreground">
              Have social links
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Search and View Toggle */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div>
              <CardTitle>Artists</CardTitle>
              <CardDescription>
                Manage your performers and artists
              </CardDescription>
            </div>
            <Tabs value={viewMode} onValueChange={(value) => setViewMode(value as "table" | "cards")}>
              <TabsList>
                <TabsTrigger value="cards">Cards</TabsTrigger>
                <TabsTrigger value="table">Table</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-2 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search artists..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <Tabs value={viewMode}>
            <TabsContent value="cards">
              {/* Cards View */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredArtists.map((artist) => (
                  <Card key={artist.id} className="overflow-hidden">
                    <CardHeader className="pb-3">
                      <div className="flex items-start space-x-4">
                        <Avatar className="w-16 h-16">
                          <AvatarImage src={artist.image_url} alt={artist.name} />
                          <AvatarFallback>{getInitials(artist.name)}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 min-w-0">
                          <CardTitle className="text-lg truncate">{artist.name}</CardTitle>
                          <Badge variant="secondary" className="mt-1">
                            {artist.role}
                          </Badge>
                          <div className="flex space-x-2 mt-2">
                            {getSocialLinks(artist).map((link, index) => (
                              <Button
                                key={index}
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                asChild
                              >
                                <a href={link.url} target="_blank" rel="noopener noreferrer">
                                  <link.icon className="h-4 w-4" />
                                </a>
                              </Button>
                            ))}
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => setSelectedArtist(artist)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                            <DropdownMenuItem>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit Artist
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDeleteArtist(artist.id)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete Artist
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      {artist.bio && (
                        <p className="text-sm text-gray-600 line-clamp-3 mb-3">
                          {artist.bio}
                        </p>
                      )}
                      <div className="flex items-center justify-between text-sm text-gray-500">
                        <span>Added {formatDate(artist.created_at)}</span>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="table">
              {/* Table View */}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Artist</TableHead>
                      <TableHead>Role</TableHead>
                      <TableHead>Social Media</TableHead>
                      <TableHead>Created</TableHead>
                      <TableHead className="text-right">Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredArtists.map((artist) => (
                      <TableRow key={artist.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center space-x-3">
                            <Avatar className="w-10 h-10">
                              <AvatarImage src={artist.image_url} alt={artist.name} />
                              <AvatarFallback>{getInitials(artist.name)}</AvatarFallback>
                            </Avatar>
                            <div>
                              <p className="font-semibold">{artist.name}</p>
                              {artist.bio && (
                                <p className="text-sm text-gray-500 truncate max-w-xs">
                                  {artist.bio}
                                </p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="secondary">
                            {artist.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            {getSocialLinks(artist).map((link, index) => (
                              <Button
                                key={index}
                                variant="ghost"
                                size="sm"
                                className="h-8 w-8 p-0"
                                asChild
                              >
                                <a href={link.url} target="_blank" rel="noopener noreferrer">
                                  <link.icon className="h-4 w-4" />
                                </a>
                              </Button>
                            ))}
                          </div>
                        </TableCell>
                        <TableCell>
                          {formatDate(artist.created_at)}
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => setSelectedArtist(artist)}>
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem>
                                <Edit className="mr-2 h-4 w-4" />
                                Edit Artist
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleDeleteArtist(artist.id)}
                                className="text-red-600"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                Delete Artist
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </TabsContent>
          </Tabs>

          {filteredArtists.length === 0 && (
            <div className="text-center py-8">
              <Music className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-semibold text-gray-900">No artists found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? "Try adjusting your search terms." : "Get started by adding a new artist."}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
