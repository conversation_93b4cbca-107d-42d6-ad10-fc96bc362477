"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Loader2,
  Calendar,
  Clock,
  Users,
  DollarSign,
  ChefHat,
  Wine,
  Music,
  MapPin,
  ArrowLeft,
  ArrowRight,
  Check,
  Save,
  CheckCircle
} from "lucide-react"
import { toast } from "sonner"
import { venueService, artistService } from "@/lib/database"
import MediaUpload from "./MediaUpload"
import { validateEvent, formatZodErrors } from "@/lib/validations"
import type { Event, CreateEventForm, Venue, Artist, MenuItem, DrinkItem } from "@/lib/types"

interface EventFormProps {
  event?: Event
  onSubmit: (data: CreateEventForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
  onSaveProgress?: (data: CreateEventForm, step: number) => Promise<void>
}

const STEPS = [
  { id: 1, title: "General Information", icon: Calendar },
  { id: 2, title: "Menu Items", icon: ChefHat },
  { id: 3, title: "Drinks", icon: Wine },
  { id: 4, title: "Artists", icon: Music },
  { id: 5, title: "Venue", icon: MapPin }
]

export default function EventForm({ event, onSubmit, onCancel, isLoading = false, onSaveProgress }: EventFormProps) {
  const [currentStep, setCurrentStep] = useState(1)
  const [venues, setVenues] = useState<Venue[]>([])
  const [artists, setArtists] = useState<Artist[]>([])
  const [formData, setFormData] = useState<CreateEventForm>({
    name: event?.name || "",
    description: event?.description || "",
    date: event?.date || "",
    start_time: event?.start_time || "",
    end_time: event?.end_time || "",
    cover_image_url: event?.cover_image_url || "",
    event_type: event?.event_type || "dinner_show",
    single_ticket_price: event?.single_ticket_price || 125,
    group_ticket_price: event?.group_ticket_price || 100,
    max_capacity: event?.max_capacity || 100,
    venue_id: event?.venue_id || "",
    artist_ids: event?.artist_ids || [],
    menu_items: event?.menu_items || [],
    drink_items: event?.drink_items || []
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [isDraft, setIsDraft] = useState(!!event?.status && event.status === 'draft')

  useEffect(() => {
    loadVenues()
    loadArtists()
  }, [])

  // Auto-save functionality
  useEffect(() => {
    if (!onSaveProgress) return

    const autoSaveInterval = setInterval(() => {
      if (formData.name.trim() && !isLoading && !isSaving) {
        handleSaveProgress()
      }
    }, 30000) // Auto-save every 30 seconds

    return () => clearInterval(autoSaveInterval)
  }, [formData, isLoading, isSaving])

  const loadVenues = async () => {
    try {
      const venueData = await venueService.getAll()
      setVenues(venueData)
    } catch (error) {
      console.error("Error loading venues:", error)
      toast.error("Failed to load venues")
    }
  }

  const loadArtists = async () => {
    try {
      const artistData = await artistService.getAll()
      setArtists(artistData)
    } catch (error) {
      console.error("Error loading artists:", error)
      toast.error("Failed to load artists")
    }
  }

  const validateStep = (step: number): boolean => {
    // For final submission, validate the entire form
    if (step === 5) {
      const result = validateEvent(formData)

      if (!result.success) {
        const formattedErrors = formatZodErrors(result.error)
        setErrors(formattedErrors)
        return false
      }

      setErrors({})
      return true
    }

    // For individual steps, validate only relevant fields
    const newErrors: Record<string, string> = {}

    switch (step) {
      case 1:
        if (!formData.name.trim()) newErrors.name = "Event name is required"
        if (!formData.description.trim()) newErrors.description = "Description is required"
        if (!formData.date) newErrors.date = "Date is required"
        if (!formData.start_time) newErrors.start_time = "Start time is required"
        if (!formData.end_time) newErrors.end_time = "End time is required"
        if (formData.single_ticket_price <= 0) newErrors.single_ticket_price = "Price must be greater than 0"
        if (formData.group_ticket_price <= 0) newErrors.group_ticket_price = "Price must be greater than 0"
        if (formData.max_capacity <= 0) newErrors.max_capacity = "Capacity must be greater than 0"

        // Validate time logic
        if (formData.start_time && formData.end_time) {
          const startTime = new Date(`2000-01-01T${formData.start_time}:00`)
          const endTime = new Date(`2000-01-01T${formData.end_time}:00`)
          if (endTime <= startTime) {
            newErrors.end_time = "End time must be after start time"
          }
        }

        // Validate pricing logic
        if (formData.group_ticket_price > formData.single_ticket_price) {
          newErrors.group_ticket_price = "Group price should be less than or equal to single price"
        }
        break
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, STEPS.length))
    } else {
      toast.error("Please fix the errors before continuing")
    }
  }

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1))
  }

  const handleSaveProgress = async () => {
    if (!onSaveProgress || !formData.name.trim()) return

    setIsSaving(true)
    try {
      console.log("EventForm - Starting save progress")
      console.log("EventForm - Form data:", formData)
      console.log("EventForm - Current step:", currentStep)

      // Ensure required fields are present for draft save
      const draftData = {
        ...formData,
        status: 'draft' as const,
        short_description: formData.description ? formData.description.substring(0, 100) : '', // Generate short description
        venue_id: formData.venue_id || '', // Ensure venue_id is not undefined
        artist_ids: formData.artist_ids || [], // Ensure artist_ids is not undefined
        menu_items: formData.menu_items || [], // Ensure menu_items is not undefined
        drink_items: formData.drink_items || [] // Ensure drink_items is not undefined
      }

      console.log("EventForm - Draft data to save:", draftData)

      // Basic validation for draft save
      if (!draftData.name.trim()) {
        throw new Error("Event name is required")
      }
      if (!draftData.description.trim()) {
        throw new Error("Event description is required")
      }

      await onSaveProgress(draftData, currentStep)
      setLastSaved(new Date())
      setIsDraft(true)
      toast.success("Progress saved!")
    } catch (error) {
      console.error("EventForm - Error saving progress:", error)

      let errorMessage = "Failed to save progress"
      if (error instanceof Error) {
        errorMessage = error.message
      } else if (typeof error === 'object' && error !== null) {
        errorMessage = JSON.stringify(error)
      }

      toast.error(errorMessage)
    } finally {
      setIsSaving(false)
    }
  }

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) {
      toast.error("Please fix the errors before submitting")
      return
    }

    try {
      // Mark as active when submitting final form
      const finalData = {
        ...formData,
        status: 'active' as const
      }

      await onSubmit(finalData)
      toast.success(event ? "Event updated successfully" : "Event created successfully")
    } catch (error) {
      console.error("Error submitting event:", error)
      toast.error("Failed to save event")
    }
  }

  const handleInputChange = (field: keyof CreateEventForm, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  const addMenuItem = () => {
    const newItem: MenuItem = {
      id: `temp-${Date.now()}`,
      name: "",
      description: "",
      image_url: "",
      category: "",
      tags: []
    }
    setFormData(prev => ({
      ...prev,
      menu_items: [...prev.menu_items, newItem]
    }))
  }

  const updateMenuItem = (index: number, field: keyof MenuItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      menu_items: prev.menu_items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const removeMenuItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      menu_items: prev.menu_items.filter((_, i) => i !== index)
    }))
  }

  const addDrinkItem = () => {
    const newItem: DrinkItem = {
      id: `temp-${Date.now()}`,
      name: "",
      description: "",
      price: 0,
      image_url: "",
      category: "",
      tags: []
    }
    setFormData(prev => ({
      ...prev,
      drink_items: [...prev.drink_items, newItem]
    }))
  }

  const updateDrinkItem = (index: number, field: keyof DrinkItem, value: any) => {
    setFormData(prev => ({
      ...prev,
      drink_items: prev.drink_items.map((item, i) => 
        i === index ? { ...item, [field]: value } : item
      )
    }))
  }

  const removeDrinkItem = (index: number) => {
    setFormData(prev => ({
      ...prev,
      drink_items: prev.drink_items.filter((_, i) => i !== index)
    }))
  }

  const toggleArtist = (artistId: string) => {
    setFormData(prev => ({
      ...prev,
      artist_ids: prev.artist_ids.includes(artistId)
        ? prev.artist_ids.filter(id => id !== artistId)
        : [...prev.artist_ids, artistId]
    }))
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="w-5 h-5" />
              {event ? "Edit Event" : "Create New Event"}
              {isDraft && (
                <span className="text-sm bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                  Draft
                </span>
              )}
            </CardTitle>
            <CardDescription>
              {event ? "Update event information" : "Create a new Jazz & Dine event"}
            </CardDescription>
          </div>

          {/* Save Progress Section */}
          <div className="flex items-center gap-2">
            {lastSaved && (
              <div className="flex items-center gap-1 text-sm text-green-600">
                <CheckCircle className="w-4 h-4" />
                <span>Saved {lastSaved.toLocaleTimeString()}</span>
              </div>
            )}

            {onSaveProgress && formData.name.trim() && (
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={handleSaveProgress}
                disabled={isSaving || isLoading}
                className="flex items-center gap-2"
              >
                {isSaving ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                {isSaving ? "Saving..." : "Save Progress"}
              </Button>
            )}
          </div>
        </div>
        
        {/* Progress Steps */}
        <div className="flex items-center justify-between mt-6">
          {STEPS.map((step, index) => (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                currentStep >= step.id 
                  ? 'bg-amber-600 border-amber-600 text-white' 
                  : 'border-gray-300 text-gray-400'
              }`}>
                {currentStep > step.id ? (
                  <Check className="w-4 h-4" />
                ) : (
                  <step.icon className="w-4 h-4" />
                )}
              </div>
              <span className={`ml-2 text-sm font-medium ${
                currentStep >= step.id ? 'text-gray-900' : 'text-gray-400'
              }`}>
                {step.title}
              </span>
              {index < STEPS.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  currentStep > step.id ? 'bg-amber-600' : 'bg-gray-300'
                }`} />
              )}
            </div>
          ))}
        </div>
      </CardHeader>
      <CardContent>
        {/* Step 1: General Information */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Event Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter event name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="event_type">Event Type</Label>
                <Select value={formData.event_type} onValueChange={(value) => handleInputChange("event_type", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select event type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dinner_show">Dinner Show</SelectItem>
                    <SelectItem value="cocktail_event">Cocktail Event</SelectItem>
                    <SelectItem value="wine_tasting">Wine Tasting</SelectItem>
                    <SelectItem value="private_dining">Private Dining</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description *</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Describe your event..."
                rows={3}
                className={errors.description ? "border-red-500" : ""}
              />
              {errors.description && <p className="text-sm text-red-500">{errors.description}</p>}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className={errors.date ? "border-red-500" : ""}
                />
                {errors.date && <p className="text-sm text-red-500">{errors.date}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="start_time">Start Time *</Label>
                <Input
                  id="start_time"
                  type="time"
                  value={formData.start_time}
                  onChange={(e) => handleInputChange("start_time", e.target.value)}
                  className={errors.start_time ? "border-red-500" : ""}
                />
                {errors.start_time && <p className="text-sm text-red-500">{errors.start_time}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="end_time">End Time *</Label>
                <Input
                  id="end_time"
                  type="time"
                  value={formData.end_time}
                  onChange={(e) => handleInputChange("end_time", e.target.value)}
                  className={errors.end_time ? "border-red-500" : ""}
                />
                {errors.end_time && <p className="text-sm text-red-500">{errors.end_time}</p>}
              </div>
            </div>

            <MediaUpload
              label="Cover Image"
              value={formData.cover_image_url}
              onChange={(url) => handleInputChange("cover_image_url", url)}
              placeholder="Upload event cover image or enter URL"
              maxSize={5}
            />

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label htmlFor="single_ticket_price">Single Ticket Price ($) *</Label>
                <Input
                  id="single_ticket_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.single_ticket_price}
                  onChange={(e) => handleInputChange("single_ticket_price", parseFloat(e.target.value) || 0)}
                  className={errors.single_ticket_price ? "border-red-500" : ""}
                />
                {errors.single_ticket_price && <p className="text-sm text-red-500">{errors.single_ticket_price}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="group_ticket_price">Group Ticket Price ($) *</Label>
                <Input
                  id="group_ticket_price"
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.group_ticket_price}
                  onChange={(e) => handleInputChange("group_ticket_price", parseFloat(e.target.value) || 0)}
                  className={errors.group_ticket_price ? "border-red-500" : ""}
                />
                {errors.group_ticket_price && <p className="text-sm text-red-500">{errors.group_ticket_price}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="max_capacity">Max Capacity *</Label>
                <Input
                  id="max_capacity"
                  type="number"
                  min="1"
                  value={formData.max_capacity}
                  onChange={(e) => handleInputChange("max_capacity", parseInt(e.target.value) || 0)}
                  className={errors.max_capacity ? "border-red-500" : ""}
                />
                {errors.max_capacity && <p className="text-sm text-red-500">{errors.max_capacity}</p>}
              </div>
            </div>
          </div>
        )}

        {/* Step 2: Menu Items */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Menu Items</h3>
              <Button type="button" onClick={addMenuItem} variant="outline" size="sm">
                <ChefHat className="w-4 h-4 mr-2" />
                Add Menu Item
              </Button>
            </div>

            {formData.menu_items.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <ChefHat className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No menu items</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by adding your first menu item.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {formData.menu_items.map((item, index) => (
                  <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Item Name</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => updateMenuItem(index, "name", e.target.value)}
                          placeholder="Menu item name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Category</Label>
                        <Input
                          value={item.category}
                          onChange={(e) => updateMenuItem(index, "category", e.target.value)}
                          placeholder="e.g., Appetizer, Main Course"
                        />
                      </div>
                      <div className="md:col-span-2 space-y-2">
                        <Label>Description</Label>
                        <Textarea
                          value={item.description}
                          onChange={(e) => updateMenuItem(index, "description", e.target.value)}
                          placeholder="Describe the menu item..."
                          rows={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <MediaUpload
                          label="Menu Item Image"
                          value={item.image_url}
                          onChange={(url) => updateMenuItem(index, "image_url", url)}
                          placeholder="Upload menu item image"
                          maxSize={3}
                          showUrlInput={false}
                        />
                      </div>
                      <div className="flex items-end">
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() => removeMenuItem(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Step 3: Drinks */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Drink Items</h3>
              <Button type="button" onClick={addDrinkItem} variant="outline" size="sm">
                <Wine className="w-4 h-4 mr-2" />
                Add Drink Item
              </Button>
            </div>

            {formData.drink_items.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <Wine className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No drink items</h3>
                <p className="mt-1 text-sm text-gray-500">Get started by adding your first drink item.</p>
              </div>
            ) : (
              <div className="space-y-4">
                {formData.drink_items.map((item, index) => (
                  <Card key={index} className="p-4">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="space-y-2">
                        <Label>Drink Name</Label>
                        <Input
                          value={item.name}
                          onChange={(e) => updateDrinkItem(index, "name", e.target.value)}
                          placeholder="Drink name"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Category</Label>
                        <Input
                          value={item.category}
                          onChange={(e) => updateDrinkItem(index, "category", e.target.value)}
                          placeholder="e.g., Wine, Cocktail, Beer"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label>Price ($)</Label>
                        <Input
                          type="number"
                          min="0"
                          step="0.01"
                          value={item.price}
                          onChange={(e) => updateDrinkItem(index, "price", parseFloat(e.target.value) || 0)}
                          placeholder="0.00"
                        />
                      </div>
                      <div className="md:col-span-2 space-y-2">
                        <Label>Description</Label>
                        <Textarea
                          value={item.description}
                          onChange={(e) => updateDrinkItem(index, "description", e.target.value)}
                          placeholder="Describe the drink..."
                          rows={2}
                        />
                      </div>
                      <div className="space-y-2">
                        <MediaUpload
                          label="Drink Image"
                          value={item.image_url}
                          onChange={(url) => updateDrinkItem(index, "image_url", url)}
                          placeholder="Upload drink image"
                          maxSize={3}
                          showUrlInput={false}
                        />
                      </div>
                      <div className="md:col-span-3 flex justify-end">
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          onClick={() => removeDrinkItem(index)}
                        >
                          Remove
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Step 4: Artists */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Select Artists</h3>

            {artists.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <Music className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No artists available</h3>
                <p className="mt-1 text-sm text-gray-500">Add artists in the Artists management section first.</p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {artists.map((artist) => (
                  <Card key={artist.id} className={`p-4 cursor-pointer transition-colors ${
                    formData.artist_ids.includes(artist.id)
                      ? 'ring-2 ring-amber-500 bg-amber-50'
                      : 'hover:bg-gray-50'
                  }`}>
                    <div className="flex items-center space-x-3">
                      <Checkbox
                        checked={formData.artist_ids.includes(artist.id)}
                        onCheckedChange={() => toggleArtist(artist.id)}
                      />
                      {artist.image_url && (
                        <img
                          src={artist.image_url}
                          alt={artist.name}
                          className="w-12 h-12 rounded-full object-cover"
                        />
                      )}
                      <div className="flex-1">
                        <h4 className="font-semibold">{artist.name}</h4>
                        <p className="text-sm text-gray-600">{artist.role}</p>
                        {artist.bio && (
                          <p className="text-xs text-gray-500 mt-1 line-clamp-2">{artist.bio}</p>
                        )}
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {formData.artist_ids.length > 0 && (
              <div className="mt-4">
                <p className="text-sm text-gray-600">
                  Selected {formData.artist_ids.length} artist{formData.artist_ids.length !== 1 ? 's' : ''}
                </p>
              </div>
            )}
          </div>
        )}

        {/* Step 5: Venue */}
        {currentStep === 5 && (
          <div className="space-y-6">
            <h3 className="text-lg font-semibold">Select Venue</h3>

            {venues.length === 0 ? (
              <div className="text-center py-8 border-2 border-dashed border-gray-300 rounded-lg">
                <MapPin className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-semibold text-gray-900">No venues available</h3>
                <p className="mt-1 text-sm text-gray-500">Add venues in the Venues management section first.</p>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="venue_id">Choose Venue *</Label>
                  <Select value={formData.venue_id} onValueChange={(value) => handleInputChange("venue_id", value)}>
                    <SelectTrigger className={errors.venue_id ? "border-red-500" : ""}>
                      <SelectValue placeholder="Select a venue" />
                    </SelectTrigger>
                    <SelectContent>
                      {venues.map((venue) => (
                        <SelectItem key={venue.id} value={venue.id}>
                          {venue.name} - {venue.address}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.venue_id && <p className="text-sm text-red-500">{errors.venue_id}</p>}
                </div>

                {formData.venue_id && (
                  <Card className="p-4 bg-gray-50">
                    {(() => {
                      const selectedVenue = venues.find(v => v.id === formData.venue_id)
                      if (!selectedVenue) return null

                      return (
                        <div className="flex items-start space-x-4">
                          {selectedVenue.image_url && (
                            <img
                              src={selectedVenue.image_url}
                              alt={selectedVenue.name}
                              className="w-20 h-20 rounded-lg object-cover"
                            />
                          )}
                          <div className="flex-1">
                            <h4 className="font-semibold">{selectedVenue.name}</h4>
                            <p className="text-sm text-gray-600">{selectedVenue.address}</p>
                            {selectedVenue.description && (
                              <p className="text-sm text-gray-500 mt-1">{selectedVenue.description}</p>
                            )}
                            {selectedVenue.map_link && (
                              <a
                                href={selectedVenue.map_link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-sm text-amber-600 hover:underline mt-1 inline-block"
                              >
                                View on Map →
                              </a>
                            )}
                          </div>
                        </div>
                      )
                    })()}
                  </Card>
                )}
              </div>
            )}
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            onClick={currentStep === 1 ? onCancel : handlePrevious}
            disabled={isLoading || isSaving}
          >
            {currentStep === 1 ? (
              "Cancel"
            ) : (
              <>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </>
            )}
          </Button>

          <div className="flex gap-2">
            {/* Save & Continue Button */}
            {onSaveProgress && formData.name.trim() && currentStep < STEPS.length && (
              <Button
                type="button"
                variant="outline"
                onClick={async () => {
                  await handleSaveProgress()
                  if (validateStep(currentStep)) {
                    handleNext()
                  }
                }}
                disabled={isLoading || isSaving}
                className="flex items-center gap-2"
              >
                {isSaving ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                Save & Continue
              </Button>
            )}

            {currentStep < STEPS.length ? (
              <Button
                type="button"
                onClick={handleNext}
                disabled={isLoading || isSaving}
                className="bg-amber-600 hover:bg-amber-700"
              >
                Next
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            ) : (
              <Button
                type="button"
                onClick={handleSubmit}
                disabled={isLoading || isSaving}
                className="bg-amber-600 hover:bg-amber-700"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    {event ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  event ? "Update Event" : "Publish Event"
                )}
              </Button>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
