// Database Types for Wine & Dine Application

export interface Venue {
  id: string;
  name: string;
  address: string;
  map_link?: string;
  image_url?: string;
  logo_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Artist {
  id: string;
  name: string;
  role: string;
  bio?: string;
  image_url?: string;
  instagram?: string;
  twitter?: string;
  website?: string;
  created_at: string;
  updated_at: string;
}

export interface Event {
  id: string;
  name: string;
  description?: string;
  short_description?: string;
  date: string;
  start_time: string;
  end_time: string;
  venue_id: string;
  cover_image_url?: string;
  event_type: string;
  single_ticket_price: number;
  group_ticket_price: number;
  max_capacity: number;
  status: 'active' | 'inactive' | 'sold_out' | 'cancelled';
  created_at: string;
  updated_at: string;
  // Relations
  venue?: Venue;
  artists?: Artist[];
  menu_items?: MenuItem[];
}

export interface EventArtist {
  id: string;
  event_id: string;
  artist_id: string;
  created_at: string;
}

export interface MenuItem {
  id: string;
  event_id: string;
  category: 'dinner' | 'drinks';
  name: string;
  description?: string;
  image_url?: string;
  price?: number;
  discounted_price?: number;
  dietary_tags: string[];
  item_type?: string;
  created_at: string;
  updated_at: string;
}

export interface Ticket {
  id: string;
  event_id: string;
  ticket_type: 'single' | 'group';
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  group_name?: string;
  group_size: number;
  total_amount: number;
  payment_status: 'pending' | 'paid' | 'failed' | 'refunded';
  booking_status: 'confirmed' | 'cancelled' | 'attended';
  dinner_selection?: string;
  special_requests?: string;
  created_at: string;
  updated_at: string;
  // Relations
  event?: Event;
  bottles?: TicketBottle[];
  group_members?: GroupMember[];
}

export interface TicketBottle {
  id: string;
  ticket_id: string;
  menu_item_id: string;
  quantity: number;
  unit_price: number;
  created_at: string;
  // Relations
  menu_item?: MenuItem;
}

export interface GroupMember {
  id: string;
  ticket_id: string;
  name: string;
  email?: string;
  dinner_selection?: string;
  has_confirmed: boolean;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface AdminUser {
  id: string;
  email: string;
  name?: string;
  role: 'admin' | 'super_admin';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

// Form Types
export interface CreateEventForm {
  name: string;
  description: string;
  short_description?: string;
  date: string;
  start_time: string;
  end_time: string;
  venue_id: string;
  cover_image_url?: string;
  event_type: string;
  single_ticket_price: number;
  group_ticket_price: number;
  max_capacity: number;
  artist_ids: string[];
  menu_items: MenuItem[];
  drink_items: DrinkItem[];
  status?: 'draft' | 'active' | 'cancelled' | 'completed';
}

export interface CreateVenueForm {
  name: string;
  address: string;
  map_link?: string;
  image_url?: string;
  logo_url?: string;
}

export interface CreateArtistForm {
  name: string;
  role: string;
  bio?: string;
  image_url?: string;
  instagram?: string;
  twitter?: string;
  website?: string;
}

export interface CreateMenuItemForm {
  event_id: string;
  category: 'dinner' | 'drinks';
  name: string;
  description?: string;
  image_url?: string;
  price?: number;
  discounted_price?: number;
  dietary_tags: string[];
  item_type?: string;
}

export interface CreateTicketForm {
  event_id: string;
  ticket_type: 'single' | 'group';
  customer_name: string;
  customer_email: string;
  customer_phone?: string;
  group_name?: string;
  group_size: number;
  dinner_selection?: string;
  special_requests?: string;
  selected_bottles: {
    menu_item_id: string;
    quantity: number;
    unit_price: number;
  }[];
  group_members?: {
    name: string;
    email?: string;
  }[];
}

// API Response Types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard Stats
export interface DashboardStats {
  total_events: number;
  upcoming_events: number;
  total_tickets_sold: number;
  total_revenue: number;
  recent_bookings: Ticket[];
  popular_events: Event[];
}

// Booking Progress (for localStorage)
export interface BookingProgress {
  event_id: string;
  ticket_type: 'single' | 'group';
  group_size: number;
  dinner_selection?: string;
  selected_bottles: string[];
  group_name?: string;
  customer_info?: {
    name: string;
    email: string;
    phone?: string;
  };
  step: 'ticket_type' | 'details' | 'payment';
}
