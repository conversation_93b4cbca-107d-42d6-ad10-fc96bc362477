"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Upload, User, Music, Instagram, Facebook, Youtube, Globe } from "lucide-react"
import { toast } from "sonner"
import type { Artist, CreateArtistForm } from "@/lib/types"

interface ArtistFormProps {
  artist?: Artist
  onSubmit: (data: CreateArtistForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function ArtistForm({ artist, onSubmit, onCancel, isLoading = false }: ArtistFormProps) {
  const [formData, setFormData] = useState<CreateArtistForm>({
    name: artist?.name || "",
    role: artist?.role || "",
    bio: artist?.bio || "",
    image_url: artist?.image_url || "",
    instagram: artist?.instagram || "",
    facebook: artist?.facebook || "",
    youtube: artist?.youtube || "",
    website: artist?.website || ""
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Artist name is required"
    }

    if (!formData.role.trim()) {
      newErrors.role = "Artist role/position is required"
    }

    if (formData.image_url && !isValidUrl(formData.image_url)) {
      newErrors.image_url = "Please enter a valid image URL"
    }

    if (formData.instagram && !isValidSocialUrl(formData.instagram, 'instagram')) {
      newErrors.instagram = "Please enter a valid Instagram URL"
    }

    if (formData.facebook && !isValidSocialUrl(formData.facebook, 'facebook')) {
      newErrors.facebook = "Please enter a valid Facebook URL"
    }

    if (formData.youtube && !isValidSocialUrl(formData.youtube, 'youtube')) {
      newErrors.youtube = "Please enter a valid YouTube URL"
    }

    if (formData.website && !isValidUrl(formData.website)) {
      newErrors.website = "Please enter a valid website URL"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const isValidSocialUrl = (url: string, platform: string): boolean => {
    if (!isValidUrl(url)) return false
    
    const domain = new URL(url).hostname.toLowerCase()
    switch (platform) {
      case 'instagram':
        return domain.includes('instagram.com')
      case 'facebook':
        return domain.includes('facebook.com') || domain.includes('fb.com')
      case 'youtube':
        return domain.includes('youtube.com') || domain.includes('youtu.be')
      default:
        return true
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }

    try {
      await onSubmit(formData)
      toast.success(artist ? "Artist updated successfully" : "Artist created successfully")
    } catch (error) {
      console.error("Error submitting artist:", error)
      toast.error("Failed to save artist")
    }
  }

  const handleInputChange = (field: keyof CreateArtistForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Music className="w-5 h-5" />
          {artist ? "Edit Artist" : "Add New Artist"}
        </CardTitle>
        <CardDescription>
          {artist ? "Update artist information" : "Create a new artist profile for your events"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Artist Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter artist name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="role">Position/Role *</Label>
                <Input
                  id="role"
                  value={formData.role}
                  onChange={(e) => handleInputChange("role", e.target.value)}
                  placeholder="e.g., Lead Vocalist, Guitarist, DJ"
                  className={errors.role ? "border-red-500" : ""}
                />
                {errors.role && (
                  <p className="text-sm text-red-500">{errors.role}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="bio">Biography</Label>
              <Textarea
                id="bio"
                value={formData.bio}
                onChange={(e) => handleInputChange("bio", e.target.value)}
                placeholder="Tell us about the artist's background and style..."
                rows={4}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="image_url" className="flex items-center gap-2">
                <Upload className="w-4 h-4" />
                Profile Image URL
              </Label>
              <Input
                id="image_url"
                value={formData.image_url}
                onChange={(e) => handleInputChange("image_url", e.target.value)}
                placeholder="https://example.com/artist-photo.jpg"
                className={errors.image_url ? "border-red-500" : ""}
              />
              {errors.image_url && (
                <p className="text-sm text-red-500">{errors.image_url}</p>
              )}
            </div>
          </div>

          {/* Social Media Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Social Media & Links</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="instagram" className="flex items-center gap-2">
                  <Instagram className="w-4 h-4" />
                  Instagram
                </Label>
                <Input
                  id="instagram"
                  value={formData.instagram}
                  onChange={(e) => handleInputChange("instagram", e.target.value)}
                  placeholder="https://instagram.com/username"
                  className={errors.instagram ? "border-red-500" : ""}
                />
                {errors.instagram && (
                  <p className="text-sm text-red-500">{errors.instagram}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="facebook" className="flex items-center gap-2">
                  <Facebook className="w-4 h-4" />
                  Facebook
                </Label>
                <Input
                  id="facebook"
                  value={formData.facebook}
                  onChange={(e) => handleInputChange("facebook", e.target.value)}
                  placeholder="https://facebook.com/username"
                  className={errors.facebook ? "border-red-500" : ""}
                />
                {errors.facebook && (
                  <p className="text-sm text-red-500">{errors.facebook}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="youtube" className="flex items-center gap-2">
                  <Youtube className="w-4 h-4" />
                  YouTube
                </Label>
                <Input
                  id="youtube"
                  value={formData.youtube}
                  onChange={(e) => handleInputChange("youtube", e.target.value)}
                  placeholder="https://youtube.com/channel/..."
                  className={errors.youtube ? "border-red-500" : ""}
                />
                {errors.youtube && (
                  <p className="text-sm text-red-500">{errors.youtube}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="website" className="flex items-center gap-2">
                  <Globe className="w-4 h-4" />
                  Website
                </Label>
                <Input
                  id="website"
                  value={formData.website}
                  onChange={(e) => handleInputChange("website", e.target.value)}
                  placeholder="https://artistwebsite.com"
                  className={errors.website ? "border-red-500" : ""}
                />
                {errors.website && (
                  <p className="text-sm text-red-500">{errors.website}</p>
                )}
              </div>
            </div>
          </div>

          {/* Preview */}
          {formData.image_url && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Preview</h3>
              <div className="border rounded-lg p-4 bg-gray-50">
                <div className="flex items-center space-x-4">
                  <img
                    src={formData.image_url}
                    alt="Artist preview"
                    className="w-16 h-16 rounded-full object-cover"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none'
                    }}
                  />
                  <div>
                    <h4 className="font-semibold">{formData.name || "Artist Name"}</h4>
                    <p className="text-sm text-gray-600">{formData.role || "Position"}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {artist ? "Updating..." : "Creating..."}
                </>
              ) : (
                artist ? "Update Artist" : "Create Artist"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
