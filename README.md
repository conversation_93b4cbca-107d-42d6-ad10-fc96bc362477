# Wine & Dine - Premium Pop-up Dining Experience

A modern Next.js application for managing premium pop-up dining events with live music performances. Features a customer-facing booking interface and comprehensive admin portal.

## 🚀 Features

### Customer Experience
- **Event Discovery**: Beautiful event pages with artist profiles and venue information
- **Interactive Menus**: Dinner and drinks menus with dietary tags and pricing
- **Flexible Booking**: Single tickets or group table reservations
- **Premium Bottles**: Pre-order wine and spirits with discounts
- **Mobile Responsive**: Optimized for all devices

### Admin Portal
- **Dashboard**: Real-time statistics and recent activity
- **Event Management**: Create and manage events with menus and pricing
- **Artist Profiles**: Manage performer information and social links
- **Venue Management**: Venue details with map integration
- **Ticket Management**: View bookings and manage guest lists
- **Secure Authentication**: Admin-only access with Supabase Auth

## 🛠 Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Database**: Supabase (PostgreSQL)
- **Authentication**: Supabase Auth
- **Styling**: Tailwind CSS + Shadcn/ui
- **Icons**: Lucide React
- **Package Manager**: PNPM

## 📋 Prerequisites

- Node.js 18+ 
- PNPM package manager
- Supabase account and project

## 🚀 Quick Start

### 1. Clone and Install

```bash
git clone <repository-url>
cd wine-dine
pnpm install
```

### 2. Environment Setup

Create `.env.local` file:

```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Stripe for payments (future feature)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_key
STRIPE_SECRET_KEY=sk_test_your_key
STRIPE_WEBHOOK_SECRET=whsec_your_secret
```

### 3. Database Setup

1. **Create Supabase Project**: Go to [supabase.com](https://supabase.com) and create a new project

2. **Run Schema**: In Supabase SQL Editor, run:
   ```sql
   -- Copy and paste contents of database/schema.sql
   ```

3. **Add Sample Data** (Optional): Run:
   ```sql
   -- Copy and paste contents of database/sample-data.sql
   ```

4. **Create Admin User**: In Supabase Auth, create a user with the email you want to use as admin

### 4. Start Development

```bash
pnpm dev
```

Visit:
- **Customer Site**: http://localhost:3000
- **Admin Portal**: http://localhost:3000/admin

## 📁 Project Structure

```
wine-dine/
├── app/                    # Next.js App Router
│   ├── admin/             # Admin portal pages
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Customer homepage
├── components/            # Reusable components
│   ├── admin/            # Admin-specific components
│   ├── booking/          # Booking flow components
│   ├── event/            # Event display components
│   └── ui/               # Shadcn/ui components
├── database/             # Database files
│   ├── schema.sql        # Database schema
│   └── sample-data.sql   # Sample data
├── lib/                  # Utilities and services
│   ├── database.ts       # Database service layer
│   ├── supabaseClient.ts # Supabase client
│   ├── types.ts          # TypeScript types
│   └── utils.ts          # Utility functions
└── public/               # Static assets
```

## 🗄 Database Schema

### Core Tables
- **venues**: Event locations and details
- **artists**: Performer profiles and social links
- **events**: Event information with pricing
- **menu_items**: Dinner and drink options
- **tickets**: Customer bookings
- **group_members**: Group table participants
- **admin_users**: Admin access control

### Key Relationships
- Events belong to venues and have many artists
- Menu items belong to events
- Tickets belong to events and can have bottles/members
- No RLS policies (simplified for MVP)

## 🔐 Admin Access

### Default Admin
- Email: `<EMAIL>` (configured in sample data)
- Must be created in Supabase Auth manually

### Adding New Admins
1. Create user in Supabase Auth
2. Add email to `admin_users` table:
   ```sql
   INSERT INTO admin_users (email, name, role) 
   VALUES ('<EMAIL>', 'Admin Name', 'admin');
   ```

## 🎨 Customization

### Branding
- Update colors in `tailwind.config.ts`
- Replace logo in `public/jazz-dine-logo.png`
- Modify fonts in `app/layout.tsx`

### Event Types
- Add new event types in database
- Update UI components as needed
- Customize pricing logic

## 🚀 Deployment

### Vercel (Recommended)
1. Connect GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Other Platforms
- Ensure Node.js 18+ support
- Set environment variables
- Build with `pnpm build`

## 🔧 Development

### Available Scripts
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

### API Endpoints
- `GET /api/events` - List events
- `GET /api/events/[id]` - Get event details
- `POST /api/tickets` - Create booking
- `GET /api/dashboard/stats` - Admin statistics

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection**: Verify Supabase URL and keys
2. **Admin Access**: Ensure user exists in both Auth and admin_users table
3. **Build Errors**: Check TypeScript types and imports
4. **Images**: Verify image URLs are accessible

### Debug Mode
Set `NODE_ENV=development` for detailed error messages.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch (`git checkout -b feature/amazing-feature`)
3. Commit changes (`git commit -m 'Add amazing feature'`)
4. Push to branch (`git push origin feature/amazing-feature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- [Next.js](https://nextjs.org/) - React framework
- [Supabase](https://supabase.com/) - Backend as a service
- [Tailwind CSS](https://tailwindcss.com/) - Utility-first CSS
- [Shadcn/ui](https://ui.shadcn.com/) - Component library
- [Lucide](https://lucide.dev/) - Icon library
