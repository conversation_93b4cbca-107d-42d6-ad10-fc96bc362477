// Database service layer for Wine & Dine application
import { supabase } from './supabaseClient';
import type {
  Event,
  Venue,
  Artist,
  MenuItem,
  Ticket,
  GroupMember,
  AdminUser,
  CreateEventForm,
  CreateVenueForm,
  CreateArtistForm,
  CreateMenuItemForm,
  CreateTicketForm,
  DashboardStats,
  PaginatedResponse,
  ApiResponse
} from './types';

// Venues
export const venueService = {
  async getAll(): Promise<Venue[]> {
    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Venue | null> {
    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(venue: CreateVenueForm): Promise<Venue> {
    const { data, error } = await supabase
      .from('venues')
      .insert(venue)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, venue: Partial<CreateVenueForm>): Promise<Venue> {
    const { data, error } = await supabase
      .from('venues')
      .update(venue)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('venues')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Artists
export const artistService = {
  async getAll(): Promise<Artist[]> {
    const { data, error } = await supabase
      .from('artists')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Artist | null> {
    const { data, error } = await supabase
      .from('artists')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(artist: CreateArtistForm): Promise<Artist> {
    const { data, error } = await supabase
      .from('artists')
      .insert(artist)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, artist: Partial<CreateArtistForm>): Promise<Artist> {
    const { data, error } = await supabase
      .from('artists')
      .update(artist)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('artists')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Events
export const eventService = {
  async getAll(includeRelations = false): Promise<Event[]> {
    let query = supabase.from('events').select('*');
    
    if (includeRelations) {
      query = supabase
        .from('events')
        .select(`
          *,
          venue:venues(*),
          artists:event_artists(artist:artists(*)),
          menu_items(*)
        `);
    }
    
    const { data, error } = await query.order('date', { ascending: false });
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string, includeRelations = true): Promise<Event | null> {
    let query = supabase.from('events').select('*');
    
    if (includeRelations) {
      query = supabase
        .from('events')
        .select(`
          *,
          venue:venues(*),
          artists:event_artists(artist:artists(*)),
          menu_items(*)
        `);
    }
    
    const { data, error } = await query.eq('id', id).single();
    
    if (error) throw error;
    return data;
  },

  async getUpcoming(limit = 10): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        venue:venues(*),
        artists:event_artists(artist:artists(*))
      `)
      .gte('date', new Date().toISOString().split('T')[0])
      .eq('status', 'active')
      .order('date')
      .limit(limit);
    
    if (error) throw error;
    return data || [];
  },

  async create(event: CreateEventForm): Promise<Event> {
    const { artist_ids, ...eventData } = event;
    
    // Create event
    const { data: newEvent, error: eventError } = await supabase
      .from('events')
      .insert(eventData)
      .select()
      .single();
    
    if (eventError) throw eventError;
    
    // Add artists
    if (artist_ids.length > 0) {
      const eventArtists = artist_ids.map(artist_id => ({
        event_id: newEvent.id,
        artist_id
      }));
      
      const { error: artistError } = await supabase
        .from('event_artists')
        .insert(eventArtists);
      
      if (artistError) throw artistError;
    }
    
    return newEvent;
  },

  async update(id: string, event: Partial<CreateEventForm>): Promise<Event> {
    const { artist_ids, ...eventData } = event;
    
    // Update event
    const { data: updatedEvent, error: eventError } = await supabase
      .from('events')
      .update(eventData)
      .eq('id', id)
      .select()
      .single();
    
    if (eventError) throw eventError;
    
    // Update artists if provided
    if (artist_ids) {
      // Remove existing artists
      await supabase
        .from('event_artists')
        .delete()
        .eq('event_id', id);
      
      // Add new artists
      if (artist_ids.length > 0) {
        const eventArtists = artist_ids.map(artist_id => ({
          event_id: id,
          artist_id
        }));
        
        const { error: artistError } = await supabase
          .from('event_artists')
          .insert(eventArtists);
        
        if (artistError) throw artistError;
      }
    }
    
    return updatedEvent;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('events')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Menu Items
export const menuService = {
  async getByEventId(eventId: string): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('event_id', eventId)
      .order('category', { ascending: true })
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async create(menuItem: CreateMenuItemForm): Promise<MenuItem> {
    const { data, error } = await supabase
      .from('menu_items')
      .insert(menuItem)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, menuItem: Partial<CreateMenuItemForm>): Promise<MenuItem> {
    const { data, error } = await supabase
      .from('menu_items')
      .update(menuItem)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('menu_items')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Tickets
export const ticketService = {
  async getAll(page = 1, limit = 50): Promise<PaginatedResponse<Ticket>> {
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from('tickets')
      .select(`
        *,
        event:events(name, date),
        bottles:ticket_bottles(*, menu_item:menu_items(*)),
        group_members(*)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  },

  async getByEventId(eventId: string): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select(`
        *,
        bottles:ticket_bottles(*, menu_item:menu_items(*)),
        group_members(*)
      `)
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async create(ticket: CreateTicketForm): Promise<Ticket> {
    const { selected_bottles, group_members, ...ticketData } = ticket;

    // Calculate total amount
    let totalAmount = ticket.ticket_type === 'single' ? 125 : 120 * ticket.group_size;

    // Add bottle costs
    if (selected_bottles) {
      totalAmount += selected_bottles.reduce((sum, bottle) =>
        sum + (bottle.unit_price * bottle.quantity), 0);
    }

    // Create ticket
    const { data: newTicket, error: ticketError } = await supabase
      .from('tickets')
      .insert({ ...ticketData, total_amount: totalAmount })
      .select()
      .single();

    if (ticketError) throw ticketError;

    // Add bottles
    if (selected_bottles && selected_bottles.length > 0) {
      const bottles = selected_bottles.map(bottle => ({
        ticket_id: newTicket.id,
        ...bottle
      }));

      const { error: bottleError } = await supabase
        .from('ticket_bottles')
        .insert(bottles);

      if (bottleError) throw bottleError;
    }

    // Add group members
    if (group_members && group_members.length > 0) {
      const members = group_members.map(member => ({
        ticket_id: newTicket.id,
        ...member
      }));

      const { error: memberError } = await supabase
        .from('group_members')
        .insert(members);

      if (memberError) throw memberError;
    }

    return newTicket;
  },

  async updateStatus(id: string, status: 'confirmed' | 'cancelled' | 'attended'): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .update({ booking_status: status })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Dashboard
export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    // Get total events
    const { count: totalEvents } = await supabase
      .from('events')
      .select('*', { count: 'exact', head: true });

    // Get upcoming events
    const { count: upcomingEvents } = await supabase
      .from('events')
      .select('*', { count: 'exact', head: true })
      .gte('date', new Date().toISOString().split('T')[0])
      .eq('status', 'active');

    // Get total tickets sold
    const { count: totalTickets } = await supabase
      .from('tickets')
      .select('*', { count: 'exact', head: true })
      .eq('booking_status', 'confirmed');

    // Get total revenue
    const { data: revenueData } = await supabase
      .from('tickets')
      .select('total_amount')
      .eq('booking_status', 'confirmed');

    const totalRevenue = revenueData?.reduce((sum, ticket) => sum + ticket.total_amount, 0) || 0;

    // Get recent bookings
    const { data: recentBookings } = await supabase
      .from('tickets')
      .select(`
        *,
        event:events(name, date)
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get popular events (by ticket count)
    const { data: popularEvents } = await supabase
      .from('events')
      .select(`
        *,
        venue:venues(name),
        ticket_count:tickets(count)
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(5);

    return {
      total_events: totalEvents || 0,
      upcoming_events: upcomingEvents || 0,
      total_tickets_sold: totalTickets || 0,
      total_revenue: totalRevenue,
      recent_bookings: recentBookings || [],
      popular_events: popularEvents || []
    };
  }
};

// Admin Users
export const adminService = {
  async isAdmin(email: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('id')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    return !error && !!data;
  },

  async getAll(): Promise<AdminUser[]> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
};
