// Database service layer for Jazz & Dine application
import { supabase } from './supabaseClient';
import type {
  Event,
  Venue,
  Artist,
  MenuItem,
  Ticket,
  GroupMember,
  AdminUser,
  CreateEventForm,
  CreateVenueForm,
  CreateArtistForm,
  CreateMenuItemForm,
  CreateTicketForm,
  DashboardStats,
  PaginatedResponse,
  ApiResponse
} from './types';

// Venues
export const venueService = {
  async getAll(): Promise<Venue[]> {
    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Venue | null> {
    const { data, error } = await supabase
      .from('venues')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(venue: CreateVenueForm): Promise<Venue> {
    const { data, error } = await supabase
      .from('venues')
      .insert(venue)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, venue: Partial<CreateVenueForm>): Promise<Venue> {
    const { data, error } = await supabase
      .from('venues')
      .update(venue)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    // First check if venue is used in any events
    const { data: events, error: checkError } = await supabase
      .from('events')
      .select('id, name')
      .eq('venue_id', id)
      .limit(1);

    if (checkError) throw checkError;

    if (events && events.length > 0) {
      throw new Error(`Cannot delete venue: it is being used by ${events.length} event(s). Please remove or reassign the events first.`);
    }

    const { error } = await supabase
      .from('venues')
      .delete()
      .eq('id', id);

    if (error) {
      // Handle common database errors
      if (error.code === '23503') {
        throw new Error('Cannot delete venue: it is referenced by other records');
      }
      throw error;
    }
  }
};

// Artists
export const artistService = {
  async getAll(): Promise<Artist[]> {
    const { data, error } = await supabase
      .from('artists')
      .select('*')
      .order('name');
    
    if (error) throw error;
    return data || [];
  },

  async getById(id: string): Promise<Artist | null> {
    const { data, error } = await supabase
      .from('artists')
      .select('*')
      .eq('id', id)
      .single();
    
    if (error) throw error;
    return data;
  },

  async create(artist: CreateArtistForm): Promise<Artist> {
    const { data, error } = await supabase
      .from('artists')
      .insert(artist)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, artist: Partial<CreateArtistForm>): Promise<Artist> {
    const { data, error } = await supabase
      .from('artists')
      .update(artist)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    // First check if artist is used in any events
    const { data: eventArtists, error: checkError } = await supabase
      .from('event_artists')
      .select('event_id')
      .eq('artist_id', id)
      .limit(1);

    if (checkError) throw checkError;

    if (eventArtists && eventArtists.length > 0) {
      throw new Error(`Cannot delete artist: they are performing in ${eventArtists.length} event(s). Please remove them from events first.`);
    }

    const { error } = await supabase
      .from('artists')
      .delete()
      .eq('id', id);

    if (error) {
      // Handle common database errors
      if (error.code === '23503') {
        throw new Error('Cannot delete artist: they are referenced by other records');
      }
      throw error;
    }
  }
};

// Events
export const eventService = {
  async getAll(includeRelations = true): Promise<Event[]> {
    let query = supabase.from('events').select('*');

    if (includeRelations) {
      query = supabase
        .from('events')
        .select(`
          *,
          venue:venues(*),
          event_artists(artist:artists(*)),
          menu_items(*),
          drink_items(*)
        `);
    }

    const { data, error } = await query.order('date', { ascending: false });

    if (error) throw error;

    // Transform the data to include artist_ids array for compatibility
    const events = (data || []).map(event => ({
      ...event,
      artist_ids: includeRelations ? (event.event_artists?.map((ea: any) => ea.artist?.id).filter(Boolean) || []) : [],
      menu_items: includeRelations ? (event.menu_items || []) : [],
      drink_items: includeRelations ? (event.drink_items || []) : []
    }));

    console.log("Database - Retrieved events with relationships:", events.length, "events");
    return events;
  },

  async getById(id: string, includeRelations = true): Promise<Event | null> {
    let query = supabase.from('events').select('*');

    if (includeRelations) {
      query = supabase
        .from('events')
        .select(`
          *,
          venue:venues(*),
          event_artists(artist:artists(*)),
          menu_items(*),
          drink_items(*)
        `);
    }

    const { data, error } = await query.eq('id', id).single();

    if (error) throw error;

    if (data && includeRelations) {
      // Transform the data to include artist_ids array for compatibility
      return {
        ...data,
        artist_ids: data.event_artists?.map((ea: any) => ea.artist?.id).filter(Boolean) || [],
        menu_items: data.menu_items || [],
        drink_items: data.drink_items || []
      };
    }

    return data;
  },

  async getUpcoming(limit = 10): Promise<Event[]> {
    const { data, error } = await supabase
      .from('events')
      .select(`
        *,
        venue:venues(*),
        artists:event_artists(artist:artists(*))
      `)
      .gte('date', new Date().toISOString().split('T')[0])
      .eq('status', 'active')
      .order('date')
      .limit(limit);
    
    if (error) throw error;
    return data || [];
  },

  async create(event: CreateEventForm): Promise<Event> {
    const { artist_ids, menu_items, drink_items, ...eventData } = event;

    console.log("Database - Creating event with data:", eventData)
    console.log("Database - Artist IDs:", artist_ids)
    console.log("Database - Menu items:", menu_items)
    console.log("Database - Drink items:", drink_items)

    // Create event
    const { data: newEvent, error: eventError } = await supabase
      .from('events')
      .insert(eventData)
      .select()
      .single();

    if (eventError) {
      console.error("Database - Event creation error:", eventError)
      throw eventError;
    }

    console.log("Database - Created event:", newEvent)
    
    // Add artists - CRITICAL: This must work for proper relationships
    if (artist_ids && artist_ids.length > 0) {
      console.log("Database - Adding artists to event:", artist_ids)

      const eventArtists = artist_ids.map(artist_id => ({
        event_id: newEvent.id,
        artist_id
      }));

      const { data: insertedArtists, error: artistError } = await supabase
        .from('event_artists')
        .insert(eventArtists)
        .select();

      if (artistError) {
        console.error("Database - Artist insertion error:", artistError)
        console.error("Database - Failed to insert artists:", eventArtists)
        // For now, don't throw to allow event creation, but log the issue
        console.warn("Database - Event created but artists not linked!")
      } else {
        console.log("Database - Successfully linked artists:", insertedArtists)
      }
    }

    // Add menu items (if any) - CRITICAL: This must work for proper data storage
    if (menu_items && menu_items.length > 0) {
      console.log("Database - Adding menu items to event:", menu_items)

      try {
        const menuItemsWithEventId = menu_items.map(item => ({
          name: item.name,
          description: item.description || null,
          category: item.category || null,
          image_url: item.image_url || null,
          tags: item.tags || [],
          event_id: newEvent.id
          // Don't include 'id' field - let database generate it
        }));

        const { data: insertedMenuItems, error: menuError } = await supabase
          .from('menu_items')
          .insert(menuItemsWithEventId)
          .select();

        if (menuError) {
          console.error("Database - Menu items insertion error:", menuError)
          console.error("Database - Failed to insert menu items:", menuItemsWithEventId)
          console.warn("Database - Event created but menu items not saved!")
        } else {
          console.log("Database - Successfully saved menu items:", insertedMenuItems)
        }
      } catch (error) {
        console.error("Database - Menu items insertion failed:", error)
      }
    }

    // Add drink items (if any) - CRITICAL: This must work for proper data storage
    if (drink_items && drink_items.length > 0) {
      console.log("Database - Adding drink items to event:", drink_items)

      try {
        const drinkItemsWithEventId = drink_items.map(item => ({
          name: item.name,
          description: item.description || null,
          price: item.price || 0,
          category: item.category || null,
          image_url: item.image_url || null,
          tags: item.tags || [],
          event_id: newEvent.id
          // Don't include 'id' field - let database generate it
        }));

        const { data: insertedDrinkItems, error: drinkError } = await supabase
          .from('drink_items')
          .insert(drinkItemsWithEventId)
          .select();

        if (drinkError) {
          console.error("Database - Drink items insertion error:", drinkError)
          console.error("Database - Failed to insert drink items:", drinkItemsWithEventId)
          console.warn("Database - Event created but drink items not saved!")
        } else {
          console.log("Database - Successfully saved drink items:", insertedDrinkItems)
        }
      } catch (error) {
        console.error("Database - Drink items insertion failed:", error)
      }
    }

    return newEvent;
  },

  async update(id: string, event: Partial<CreateEventForm>): Promise<Event> {
    const { artist_ids, menu_items, drink_items, ...eventData } = event;

    console.log("Database - Updating event with data:", eventData)
    console.log("Database - Artist IDs:", artist_ids)
    console.log("Database - Menu items:", menu_items)
    console.log("Database - Drink items:", drink_items)

    // Update event
    const { data: updatedEvent, error: eventError } = await supabase
      .from('events')
      .update(eventData)
      .eq('id', id)
      .select()
      .single();

    if (eventError) {
      console.error("Database - Event update error:", eventError)
      throw eventError;
    }

    console.log("Database - Updated event:", updatedEvent)
    
    // Update artists if provided
    if (artist_ids !== undefined) {
      // Remove existing artists
      await supabase
        .from('event_artists')
        .delete()
        .eq('event_id', id);

      // Add new artists
      if (artist_ids.length > 0) {
        const eventArtists = artist_ids.map(artist_id => ({
          event_id: id,
          artist_id
        }));

        const { error: artistError } = await supabase
          .from('event_artists')
          .insert(eventArtists);

        if (artistError) {
          console.error("Database - Artist update error:", artistError)
          // Don't throw for draft saves
        }
      }
    }

    // Update menu items if provided - Skip if table doesn't exist
    if (menu_items !== undefined) {
      try {
        // Remove existing menu items
        await supabase
          .from('menu_items')
          .delete()
          .eq('event_id', id);

        // Add new menu items
        if (menu_items.length > 0) {
          const menuItemsWithEventId = menu_items.map(item => ({
            ...item,
            event_id: id,
            id: undefined // Remove temp IDs
          }));

          const { error: menuError } = await supabase
            .from('menu_items')
            .insert(menuItemsWithEventId);

          if (menuError) {
            console.warn("Database - Menu items update error:", menuError)
            // Don't throw for draft saves
          }
        }
      } catch (error) {
        console.warn("Database - Skipping menu items update:", error)
      }
    }

    // Update drink items if provided - Skip if table doesn't exist
    if (drink_items !== undefined) {
      try {
        // Remove existing drink items
        await supabase
          .from('drink_items')
          .delete()
          .eq('event_id', id);

        // Add new drink items
        if (drink_items.length > 0) {
          const drinkItemsWithEventId = drink_items.map(item => ({
            ...item,
            event_id: id,
            id: undefined // Remove temp IDs
          }));

          const { error: drinkError } = await supabase
            .from('drink_items')
            .insert(drinkItemsWithEventId);

          if (drinkError) {
            console.warn("Database - Drink items update error:", drinkError)
            // Don't throw for draft saves
          }
        }
      } catch (error) {
        console.warn("Database - Skipping drink items update:", error)
      }
    }

    return updatedEvent;
  },

  async delete(id: string): Promise<void> {
    try {
      // Delete related records first to avoid foreign key constraints

      // Delete event-artist relationships
      const { error: artistError } = await supabase
        .from('event_artists')
        .delete()
        .eq('event_id', id);

      if (artistError) {
        console.warn('Error deleting event artists:', artistError);
        // Don't throw here, continue with deletion
      }

      // Delete menu items
      const { error: menuError } = await supabase
        .from('menu_items')
        .delete()
        .eq('event_id', id);

      if (menuError) {
        console.warn('Error deleting menu items:', menuError);
        // Don't throw here, continue with deletion
      }

      // Delete tickets (if any)
      const { error: ticketError } = await supabase
        .from('tickets')
        .delete()
        .eq('event_id', id);

      if (ticketError) {
        console.warn('Error deleting tickets:', ticketError);
        // Don't throw here, continue with deletion
      }

      // Finally delete the event
      const { error } = await supabase
        .from('events')
        .delete()
        .eq('id', id);

      if (error) {
        console.error('Error deleting event:', error);
        throw new Error(`Failed to delete event: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Event deletion error:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete event: Unknown error occurred');
    }
  }
};

// Menu Items
export const menuService = {
  async getByEventId(eventId: string): Promise<MenuItem[]> {
    const { data, error } = await supabase
      .from('menu_items')
      .select('*')
      .eq('event_id', eventId)
      .order('category', { ascending: true })
      .order('name', { ascending: true });
    
    if (error) throw error;
    return data || [];
  },

  async create(menuItem: CreateMenuItemForm): Promise<MenuItem> {
    const { data, error } = await supabase
      .from('menu_items')
      .insert(menuItem)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async update(id: string, menuItem: Partial<CreateMenuItemForm>): Promise<MenuItem> {
    const { data, error } = await supabase
      .from('menu_items')
      .update(menuItem)
      .eq('id', id)
      .select()
      .single();
    
    if (error) throw error;
    return data;
  },

  async delete(id: string): Promise<void> {
    const { error } = await supabase
      .from('menu_items')
      .delete()
      .eq('id', id);
    
    if (error) throw error;
  }
};

// Tickets
export const ticketService = {
  async getAll(page = 1, limit = 50): Promise<PaginatedResponse<Ticket>> {
    const offset = (page - 1) * limit;

    const { data, error, count } = await supabase
      .from('tickets')
      .select(`
        *,
        event:events(name, date),
        bottles:ticket_bottles(*, menu_item:menu_items(*)),
        group_members(*)
      `, { count: 'exact' })
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) throw error;

    return {
      data: data || [],
      total: count || 0,
      page,
      limit,
      totalPages: Math.ceil((count || 0) / limit)
    };
  },

  async getByEventId(eventId: string): Promise<Ticket[]> {
    const { data, error } = await supabase
      .from('tickets')
      .select(`
        *,
        bottles:ticket_bottles(*, menu_item:menu_items(*)),
        group_members(*)
      `)
      .eq('event_id', eventId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  },

  async create(ticket: CreateTicketForm): Promise<Ticket> {
    const { selected_bottles, group_members, ...ticketData } = ticket;

    // Calculate total amount
    let totalAmount = ticket.ticket_type === 'single' ? 125 : 120 * ticket.group_size;

    // Add bottle costs
    if (selected_bottles) {
      totalAmount += selected_bottles.reduce((sum, bottle) =>
        sum + (bottle.unit_price * bottle.quantity), 0);
    }

    // Create ticket
    const { data: newTicket, error: ticketError } = await supabase
      .from('tickets')
      .insert({ ...ticketData, total_amount: totalAmount })
      .select()
      .single();

    if (ticketError) throw ticketError;

    // Add bottles
    if (selected_bottles && selected_bottles.length > 0) {
      const bottles = selected_bottles.map(bottle => ({
        ticket_id: newTicket.id,
        ...bottle
      }));

      const { error: bottleError } = await supabase
        .from('ticket_bottles')
        .insert(bottles);

      if (bottleError) throw bottleError;
    }

    // Add group members
    if (group_members && group_members.length > 0) {
      const members = group_members.map(member => ({
        ticket_id: newTicket.id,
        ...member
      }));

      const { error: memberError } = await supabase
        .from('group_members')
        .insert(members);

      if (memberError) throw memberError;
    }

    return newTicket;
  },

  async updateStatus(id: string, status: 'confirmed' | 'cancelled' | 'attended'): Promise<Ticket> {
    const { data, error } = await supabase
      .from('tickets')
      .update({ booking_status: status })
      .eq('id', id)
      .select()
      .single();

    if (error) throw error;
    return data;
  }
};

// Dashboard
export const dashboardService = {
  async getStats(): Promise<DashboardStats> {
    // Get total events
    const { count: totalEvents } = await supabase
      .from('events')
      .select('*', { count: 'exact', head: true });

    // Get upcoming events
    const { count: upcomingEvents } = await supabase
      .from('events')
      .select('*', { count: 'exact', head: true })
      .gte('date', new Date().toISOString().split('T')[0])
      .eq('status', 'active');

    // Get total tickets sold
    const { count: totalTickets } = await supabase
      .from('tickets')
      .select('*', { count: 'exact', head: true })
      .eq('booking_status', 'confirmed');

    // Get total revenue
    const { data: revenueData } = await supabase
      .from('tickets')
      .select('total_amount')
      .eq('booking_status', 'confirmed');

    const totalRevenue = revenueData?.reduce((sum, ticket) => sum + ticket.total_amount, 0) || 0;

    // Get recent bookings
    const { data: recentBookings } = await supabase
      .from('tickets')
      .select(`
        *,
        event:events(name, date)
      `)
      .order('created_at', { ascending: false })
      .limit(5);

    // Get popular events (by ticket count)
    const { data: popularEvents } = await supabase
      .from('events')
      .select(`
        *,
        venue:venues(name),
        ticket_count:tickets(count)
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(5);

    return {
      total_events: totalEvents || 0,
      upcoming_events: upcomingEvents || 0,
      total_tickets_sold: totalTickets || 0,
      total_revenue: totalRevenue,
      recent_bookings: recentBookings || [],
      popular_events: popularEvents || []
    };
  }
};

// Admin Users
export const adminService = {
  async isAdmin(email: string): Promise<boolean> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('id')
      .eq('email', email)
      .eq('is_active', true)
      .single();

    return !error && !!data;
  },

  async getAll(): Promise<AdminUser[]> {
    const { data, error } = await supabase
      .from('admin_users')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data || [];
  }
};
