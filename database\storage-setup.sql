-- Storage bucket setup for Jazz & Dine application
-- Run this in your Supabase SQL editor to set up the media storage bucket

-- Create the media bucket if it doesn't exist
INSERT INTO storage.buckets (id, name, public)
VALUES ('media', 'media', true)
ON CONFLICT (id) DO NOTHING;

-- Set up RLS policies for the media bucket
-- Allow authenticated users to upload files
CREATE POLICY "Allow authenticated users to upload media" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'media' 
  AND auth.role() = 'authenticated'
);

-- Allow public read access to all files in media bucket
CREATE POLICY "Allow public read access to media" ON storage.objects
FOR SELECT USING (bucket_id = 'media');

-- Allow authenticated users to update their own files
CREATE POLICY "Allow authenticated users to update media" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'media' 
  AND auth.role() = 'authenticated'
);

-- Allow authenticated users to delete files
CREATE POLICY "Allow authenticated users to delete media" ON storage.objects
FOR DELETE USING (
  bucket_id = 'media' 
  AND auth.role() = 'authenticated'
);

-- Alternative: Simple policy for development (less secure)
-- Uncomment these and comment out the above policies if you want simpler access control

-- CREATE POLICY "Allow all operations on media bucket" ON storage.objects
-- FOR ALL USING (bucket_id = 'media');

-- Note: For production, you should implement more restrictive policies
-- that check user permissions and file ownership
