-- Storage bucket setup for Jazz & Dine application
-- Run this in your Supabase SQL editor to set up the media storage bucket

-- Create the media bucket if it doesn't exist (public bucket for easy access)
INSERT INTO storage.buckets (id, name, public)
VALUES ('media', 'media', true)
ON CONFLICT (id) DO NOTHING;

-- Disable RLS on storage.objects for the media bucket (simpler approach)
-- This allows unrestricted access to the media bucket for development

-- First, drop any existing policies for the media bucket
DROP POLICY IF EXISTS "Allow authenticated users to upload media" ON storage.objects;
DROP POLICY IF EXISTS "Allow public read access to media" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to update media" ON storage.objects;
DROP POLICY IF EXISTS "Allow authenticated users to delete media" ON storage.objects;
DROP POLICY IF EXISTS "Allow all operations on media bucket" ON storage.objects;

-- Disable RLS for storage.objects table (this affects all buckets)
-- Note: This is a simple approach for development. For production, consider bucket-specific policies.
ALTER TABLE storage.objects DISABLE ROW LEVEL SECURITY;

-- Alternative: If you want to keep R<PERSON> enabled but allow all operations on media bucket only:
-- ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
-- CREATE POLICY "Allow all operations on media bucket" ON storage.objects
-- FOR ALL USING (bucket_id = 'media');

-- Verify the bucket exists and is public
-- You can run this query to check:
-- SELECT * FROM storage.buckets WHERE id = 'media';
