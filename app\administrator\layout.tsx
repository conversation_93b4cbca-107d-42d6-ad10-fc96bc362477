"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabaseClient"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarInset
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Input } from "@/components/ui/input"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  Wine,
  LayoutDashboard,
  Calendar,
  MapPin,
  Users,
  TrendingUp,
  <PERSON><PERSON><PERSON>,
  Log<PERSON><PERSON>,
  User,
  <PERSON><PERSON>,
  <PERSON>,
  Bell,
  HelpCircle
} from "lucide-react"
import { toast } from "sonner"

const navigation = [
  {
    title: "Dashboard",
    url: "/administrator/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "My Task",
    url: "/administrator/events",
    icon: Calendar,
  },
  {
    title: "Meal Planner",
    url: "/administrator/venues",
    icon: MapPin,
  },
  {
    title: "Documents",
    url: "/administrator/artists",
    icon: Users,
  },
  {
    title: "Receipts",
    url: "/administrator/sales",
    icon: TrendingUp,
  },
  {
    title: "Chats",
    url: "/administrator/settings",
    icon: Settings,
  },
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        router.push("/auth/login")
        return
      }

      // Verify admin status
      const { data: adminData, error: adminError } = await supabase
        .from('admin_users')
        .select('id, is_active, name, role')
        .eq('email', user.email)
        .eq('is_active', true)
        .single()

      if (adminError || !adminData) {
        await supabase.auth.signOut()
        router.push("/auth/login")
        return
      }

      setUser({ ...user, ...adminData })
    } catch (error) {
      console.error("Auth check error:", error)
      router.push("/auth/login")
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        toast.error("Logout failed", {
          description: error.message,
        })
        return
      }

      toast.success("Logged out successfully")
      router.push("/auth/login")
    } catch (error) {
      console.error("Logout error:", error)
      toast.error("Logout failed", {
        description: "An unexpected error occurred.",
      })
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Wine className="w-8 h-8 animate-spin mx-auto mb-4 text-amber-600" />
          <p className="text-gray-600">Loading admin portal...</p>
        </div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-white">
        <Sidebar className="border-r border-gray-100 bg-white" collapsible="icon">
          <SidebarHeader className="border-b border-gray-100 px-4 py-4">
            <div className="flex items-center space-x-3 group-data-[collapsible=icon]:justify-center">
              <div className="w-8 h-8 bg-black rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-lg">S</span>
              </div>
              <div className="group-data-[collapsible=icon]:hidden">
                <h2 className="text-lg font-bold text-gray-900">Sundays.</h2>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent className="px-3 py-6">
            <div className="space-y-1">
              <p className="text-xs font-medium text-gray-400 uppercase tracking-wider px-3 mb-4 group-data-[collapsible=icon]:hidden">
                Menu
              </p>
              <SidebarMenu className="space-y-1">
                {navigation.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                      className={`w-full justify-start h-10 px-3 rounded-lg transition-all duration-200 group-data-[collapsible=icon]:justify-center ${
                        pathname === item.url
                          ? 'bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 shadow-sm'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <a href={item.url} className="flex items-center space-x-3 w-full group-data-[collapsible=icon]:justify-center">
                        <item.icon className={`w-5 h-5 ${
                          pathname === item.url ? 'text-purple-600' : 'text-gray-400'
                        }`} />
                        <span className="font-medium text-sm group-data-[collapsible=icon]:hidden">{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </div>
          </SidebarContent>

          <SidebarFooter className="border-t border-gray-100 px-3 py-4">
            <div className="space-y-4">
              {/* Upgrade Card - Only show when expanded */}
              <div className="group-data-[collapsible=icon]:hidden">
                <div className="bg-gradient-to-br from-orange-50 to-pink-50 rounded-lg p-4 border border-orange-100">
                  <h3 className="font-semibold text-gray-900 text-sm mb-1">Upgrade your plan</h3>
                  <p className="text-xs text-gray-600 mb-3 leading-relaxed">
                    Your trial plan ends in 12 days. Upgrade your plan and unlock full potential!
                  </p>
                  <Button className="w-full bg-black hover:bg-gray-800 text-white text-xs h-8">
                    ✨ See plans
                  </Button>
                </div>
              </div>

              {/* Settings and Help - Always show */}
              <div className="space-y-1">
                <SidebarMenuButton className="w-full justify-start h-9 px-3 text-gray-600 hover:bg-gray-50 group-data-[collapsible=icon]:justify-center">
                  <Settings className="w-4 h-4" />
                  <span className="text-sm group-data-[collapsible=icon]:hidden">Settings</span>
                </SidebarMenuButton>
                <SidebarMenuButton className="w-full justify-start h-9 px-3 text-gray-600 hover:bg-gray-50 group-data-[collapsible=icon]:justify-center">
                  <HelpCircle className="w-4 h-4" />
                  <span className="text-sm group-data-[collapsible=icon]:hidden">Help & Support</span>
                </SidebarMenuButton>
              </div>
            </div>
          </SidebarFooter>
        </Sidebar>

        <SidebarInset className="flex-1">
          <header className="sticky top-0 z-50 flex h-16 shrink-0 items-center gap-4 border-b border-gray-100 px-6 bg-white/80 backdrop-blur-md">
            <div className="flex items-center gap-4">
              <SidebarTrigger className="-ml-1 p-2 hover:bg-gray-100 rounded-lg transition-colors">
                <Menu className="w-4 h-4" />
              </SidebarTrigger>
              <h1 className="text-lg font-semibold text-gray-900">
                {navigation.find(item => item.url === pathname)?.title || 'My Task'}
              </h1>
            </div>

            <div className="flex-1 flex items-center justify-center max-w-md mx-auto">
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search or type a command"
                  className="w-full pl-10 pr-12 h-9 bg-gray-50 border-gray-200 focus:bg-white focus:border-gray-300 text-sm"
                />
                <kbd className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none inline-flex h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium text-muted-foreground opacity-100">
                  ⌘F
                </kbd>
              </div>
            </div>

            <div className="flex items-center gap-3">
              <Button variant="ghost" size="sm" className="h-9 w-9 p-0 relative">
                <Bell className="h-4 w-4" />
                <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full text-[10px] text-white flex items-center justify-center">
                  1
                </span>
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" className="h-9 w-9 p-0 rounded-full">
                    <Avatar className="h-8 w-8">
                      <AvatarImage src={user?.avatar_url} alt={user?.name} />
                      <AvatarFallback className="bg-gradient-to-br from-purple-100 to-pink-100 text-purple-700 text-xs font-semibold">
                        {getInitials(user?.name || user?.email || 'U')}
                      </AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-56">
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {user?.name || user?.email}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem>
                    <User className="mr-2 h-4 w-4" />
                    <span>Profile</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleLogout} className="text-red-600 focus:text-red-600">
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Sign out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </header>

          <main className="flex-1 overflow-auto bg-gray-50">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
