"use client"

import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { supabase } from "@/lib/supabaseClient"
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarProvider,
  SidebarTrigger,
  SidebarInset
} from "@/components/ui/sidebar"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import {
  Wine,
  LayoutDashboard,
  Calendar,
  MapPin,
  Users,
  TrendingUp,
  Settings,
  LogOut,
  User,
  Menu
} from "lucide-react"
import { toast } from "sonner"

const navigation = [
  {
    title: "Dashboard",
    url: "/administrator/dashboard",
    icon: LayoutDashboard,
  },
  {
    title: "Events",
    url: "/administrator/events",
    icon: Calendar,
  },
  {
    title: "Venues",
    url: "/administrator/venues",
    icon: MapPin,
  },
  {
    title: "Artists",
    url: "/administrator/artists",
    icon: Users,
  },
  {
    title: "Sales Reports",
    url: "/administrator/sales",
    icon: TrendingUp,
  },
  {
    title: "Settings",
    url: "/administrator/settings",
    icon: Settings,
  },
]

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()

  useEffect(() => {
    checkAuth()
  }, [])

  const checkAuth = async () => {
    try {
      const { data: { user }, error } = await supabase.auth.getUser()
      
      if (error || !user) {
        router.push("/auth/login")
        return
      }

      // Verify admin status
      const { data: adminData, error: adminError } = await supabase
        .from('admin_users')
        .select('id, is_active, name, role')
        .eq('email', user.email)
        .eq('is_active', true)
        .single()

      if (adminError || !adminData) {
        await supabase.auth.signOut()
        router.push("/auth/login")
        return
      }

      setUser({ ...user, ...adminData })
    } catch (error) {
      console.error("Auth check error:", error)
      router.push("/auth/login")
    } finally {
      setIsLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      if (error) {
        toast.error("Logout failed", {
          description: error.message,
        })
        return
      }

      toast.success("Logged out successfully")
      router.push("/auth/login")
    } catch (error) {
      console.error("Logout error:", error)
      toast.error("Logout failed", {
        description: "An unexpected error occurred.",
      })
    }
  }

  const getInitials = (name: string) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <Wine className="w-8 h-8 animate-spin mx-auto mb-4 text-amber-600" />
          <p className="text-gray-600">Loading admin portal...</p>
        </div>
      </div>
    )
  }

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-gray-50">
        <Sidebar className="border-r border-gray-200 bg-white">
          <SidebarHeader className="border-b border-gray-100 px-6 py-6">
            <div className="flex items-center space-x-3">
              <div className="w-9 h-9 bg-gradient-to-br from-amber-500 to-orange-500 rounded-xl flex items-center justify-center shadow-sm">
                <Wine className="w-5 h-5 text-white" />
              </div>
              <div>
                <h2 className="text-lg font-bold text-gray-900">Wine & Dine</h2>
                <p className="text-xs text-gray-500 font-medium">Admin Portal</p>
              </div>
            </div>
          </SidebarHeader>

          <SidebarContent className="px-4 py-6">
            <div className="space-y-1">
              <p className="text-xs font-semibold text-gray-400 uppercase tracking-wider px-3 mb-3">
                Menu
              </p>
              <SidebarMenu className="space-y-1">
                {navigation.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                      className={`w-full justify-start h-10 px-3 rounded-lg transition-all duration-200 ${
                        pathname === item.url
                          ? 'bg-amber-50 text-amber-700 border border-amber-200 shadow-sm'
                          : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                      }`}
                    >
                      <a href={item.url} className="flex items-center space-x-3 w-full">
                        <item.icon className={`w-4 h-4 ${
                          pathname === item.url ? 'text-amber-600' : 'text-gray-400'
                        }`} />
                        <span className="font-medium text-sm">{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </div>
          </SidebarContent>

          <SidebarFooter className="border-t border-gray-100 px-4 py-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-3 px-3 py-2 rounded-lg bg-gray-50">
                <Avatar className="w-8 h-8">
                  <AvatarImage src={user?.avatar_url} alt={user?.name} />
                  <AvatarFallback className="bg-amber-100 text-amber-700 text-xs font-semibold">
                    {getInitials(user?.name || user?.email || 'U')}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-semibold text-gray-900 truncate">
                    {user?.name || user?.email}
                  </p>
                  <p className="text-xs text-gray-500 capitalize">
                    {user?.role || 'Admin'}
                  </p>
                </div>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={handleLogout}
                className="w-full justify-start h-9 px-3 text-gray-600 hover:text-red-600 hover:bg-red-50 transition-colors"
              >
                <LogOut className="w-4 h-4 mr-3" />
                <span className="text-sm font-medium">Sign out</span>
              </Button>
            </div>
          </SidebarFooter>
        </Sidebar>

        <SidebarInset className="flex-1">
          <header className="flex h-16 shrink-0 items-center gap-4 border-b border-gray-200 px-6 bg-white">
            <SidebarTrigger className="-ml-1 p-2 hover:bg-gray-100 rounded-lg transition-colors">
              <Menu className="w-4 h-4" />
            </SidebarTrigger>
            <Separator orientation="vertical" className="h-6 bg-gray-200" />
            <div className="flex items-center space-x-2">
              <h1 className="text-xl font-bold text-gray-900">
                {navigation.find(item => item.url === pathname)?.title || 'Admin Portal'}
              </h1>
            </div>
          </header>

          <main className="flex-1 overflow-auto bg-gray-50">
            {children}
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  )
}
