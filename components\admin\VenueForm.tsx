"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Loader2, Upload, MapPin, Building, Image, ExternalLink } from "lucide-react"
import { toast } from "sonner"
import type { Venue, CreateVenueForm } from "@/lib/types"

interface VenueFormProps {
  venue?: Venue
  onSubmit: (data: CreateVenueForm) => Promise<void>
  onCancel: () => void
  isLoading?: boolean
}

export default function VenueForm({ venue, onSubmit, onCancel, isLoading = false }: VenueFormProps) {
  const [formData, setFormData] = useState<CreateVenueForm>({
    name: venue?.name || "",
    address: venue?.address || "",
    logo_url: venue?.logo_url || "",
    image_url: venue?.image_url || "",
    map_link: venue?.map_link || "",
    description: venue?.description || ""
  })
  
  const [errors, setErrors] = useState<Record<string, string>>({})

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "Venue name is required"
    }

    if (!formData.address.trim()) {
      newErrors.address = "Address is required"
    }

    if (formData.map_link && !isValidUrl(formData.map_link)) {
      newErrors.map_link = "Please enter a valid Google Maps URL"
    }

    if (formData.logo_url && !isValidUrl(formData.logo_url)) {
      newErrors.logo_url = "Please enter a valid image URL"
    }

    if (formData.image_url && !isValidUrl(formData.image_url)) {
      newErrors.image_url = "Please enter a valid image URL"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const isValidUrl = (string: string): boolean => {
    try {
      new URL(string)
      return true
    } catch (_) {
      return false
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast.error("Please fix the form errors")
      return
    }

    try {
      await onSubmit(formData)
      toast.success(venue ? "Venue updated successfully" : "Venue created successfully")
    } catch (error) {
      console.error("Error submitting venue:", error)
      toast.error("Failed to save venue")
    }
  }

  const handleInputChange = (field: keyof CreateVenueForm, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }))
    }
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="w-5 h-5" />
          {venue ? "Edit Venue" : "Add New Venue"}
        </CardTitle>
        <CardDescription>
          {venue ? "Update venue information" : "Create a new venue for your events"}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Basic Information</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Venue Name *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange("name", e.target.value)}
                  placeholder="Enter venue name"
                  className={errors.name ? "border-red-500" : ""}
                />
                {errors.name && (
                  <p className="text-sm text-red-500">{errors.name}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address *</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => handleInputChange("address", e.target.value)}
                  placeholder="Enter venue address"
                  className={errors.address ? "border-red-500" : ""}
                />
                {errors.address && (
                  <p className="text-sm text-red-500">{errors.address}</p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={formData.description}
                onChange={(e) => handleInputChange("description", e.target.value)}
                placeholder="Enter venue description (optional)"
                rows={3}
              />
            </div>
          </div>

          {/* Images */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Images</h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="logo_url" className="flex items-center gap-2">
                  <Upload className="w-4 h-4" />
                  Logo URL
                </Label>
                <Input
                  id="logo_url"
                  value={formData.logo_url}
                  onChange={(e) => handleInputChange("logo_url", e.target.value)}
                  placeholder="https://example.com/logo.jpg"
                  className={errors.logo_url ? "border-red-500" : ""}
                />
                {errors.logo_url && (
                  <p className="text-sm text-red-500">{errors.logo_url}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="image_url" className="flex items-center gap-2">
                  <Image className="w-4 h-4" />
                  Gallery Image URL
                </Label>
                <Input
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) => handleInputChange("image_url", e.target.value)}
                  placeholder="https://example.com/venue.jpg"
                  className={errors.image_url ? "border-red-500" : ""}
                />
                {errors.image_url && (
                  <p className="text-sm text-red-500">{errors.image_url}</p>
                )}
              </div>
            </div>
          </div>

          {/* Map Link */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Location</h3>
            
            <div className="space-y-2">
              <Label htmlFor="map_link" className="flex items-center gap-2">
                <MapPin className="w-4 h-4" />
                Google Maps Link
              </Label>
              <Input
                id="map_link"
                value={formData.map_link}
                onChange={(e) => handleInputChange("map_link", e.target.value)}
                placeholder="https://maps.google.com/..."
                className={errors.map_link ? "border-red-500" : ""}
              />
              {errors.map_link && (
                <p className="text-sm text-red-500">{errors.map_link}</p>
              )}
              <p className="text-sm text-gray-500">
                Add a Google Maps link to help customers find your venue
              </p>
            </div>
          </div>

          {/* Preview */}
          {(formData.logo_url || formData.image_url) && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Preview</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {formData.logo_url && (
                  <div className="space-y-2">
                    <Label>Logo Preview</Label>
                    <div className="border rounded-lg p-4 bg-gray-50">
                      <img
                        src={formData.logo_url}
                        alt="Logo preview"
                        className="w-20 h-20 object-contain mx-auto"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    </div>
                  </div>
                )}
                {formData.image_url && (
                  <div className="space-y-2">
                    <Label>Gallery Preview</Label>
                    <div className="border rounded-lg p-2 bg-gray-50">
                      <img
                        src={formData.image_url}
                        alt="Gallery preview"
                        className="w-full h-32 object-cover rounded"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="flex justify-end space-x-3 pt-6 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isLoading}
              className="bg-amber-600 hover:bg-amber-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  {venue ? "Updating..." : "Creating..."}
                </>
              ) : (
                venue ? "Update Venue" : "Create Venue"
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
