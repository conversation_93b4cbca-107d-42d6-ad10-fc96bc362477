-- Fix check constraints that are blocking menu_items and drink_items insertion
-- Run this in Supabase SQL Editor

-- =====================================================
-- STEP 1: Check existing constraints
-- =====================================================

SELECT 
    'Current Constraints' as info,
    conname as constraint_name,
    conrelid::regclass as table_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid IN (
    'menu_items'::regclass, 
    'drink_items'::regclass
) 
AND contype = 'c';  -- Check constraints

-- =====================================================
-- STEP 2: Drop problematic check constraints
-- =====================================================

-- Drop menu_items category constraint if it exists
DO $$ 
BEGIN
    IF EXISTS (
        SELECT 1 FROM pg_constraint 
        WHERE conname = 'menu_items_category_check' 
        AND conrelid = 'menu_items'::regclass
    ) THEN
        ALTER TABLE menu_items DROP CONSTRAINT menu_items_category_check;
        RAISE NOTICE 'Dropped menu_items_category_check constraint';
    END IF;
END $$;

-- Drop any other restrictive constraints on menu_items
DO $$ 
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'menu_items'::regclass 
        AND contype = 'c'
        AND conname LIKE '%_check'
    LOOP
        EXECUTE 'ALTER TABLE menu_items DROP CONSTRAINT ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- Drop any restrictive constraints on drink_items
DO $$ 
DECLARE
    constraint_record RECORD;
BEGIN
    FOR constraint_record IN 
        SELECT conname 
        FROM pg_constraint 
        WHERE conrelid = 'drink_items'::regclass 
        AND contype = 'c'
        AND conname LIKE '%_check'
    LOOP
        EXECUTE 'ALTER TABLE drink_items DROP CONSTRAINT ' || constraint_record.conname;
        RAISE NOTICE 'Dropped constraint: %', constraint_record.conname;
    END LOOP;
END $$;

-- =====================================================
-- STEP 3: Verify constraints are removed
-- =====================================================

SELECT 
    'Remaining Constraints After Cleanup' as info,
    conname as constraint_name,
    conrelid::regclass as table_name,
    pg_get_constraintdef(oid) as constraint_definition
FROM pg_constraint 
WHERE conrelid IN (
    'menu_items'::regclass, 
    'drink_items'::regclass
) 
AND contype = 'c';

-- =====================================================
-- STEP 4: Test insertion with the problematic data
-- =====================================================

-- Test menu item insertion
INSERT INTO menu_items (event_id, name, description, category, image_url, tags)
VALUES (
    (SELECT id FROM events LIMIT 1),  -- Use any existing event
    'Test Menu Item',
    'Test menu desc',
    'Main',  -- This was causing the error
    'menu.jpg',
    ARRAY['tag1', 'tag2']
)
ON CONFLICT DO NOTHING;

-- Test drink item insertion
INSERT INTO drink_items (event_id, name, description, price, category, image_url, tags)
VALUES (
    (SELECT id FROM events LIMIT 1),  -- Use any existing event
    'Test Drink',
    'Test drink desc',
    12.50,
    'Wine',
    'drink.jpg',
    ARRAY['wine', 'red']
)
ON CONFLICT DO NOTHING;

-- =====================================================
-- STEP 5: Verify the test insertions worked
-- =====================================================

SELECT 'Menu Items Test' as test_type, COUNT(*) as count 
FROM menu_items 
WHERE name = 'Test Menu Item';

SELECT 'Drink Items Test' as test_type, COUNT(*) as count 
FROM drink_items 
WHERE name = 'Test Drink';

-- =====================================================
-- STEP 6: Clean up test data
-- =====================================================

DELETE FROM menu_items WHERE name = 'Test Menu Item';
DELETE FROM drink_items WHERE name = 'Test Drink';

-- =====================================================
-- STEP 7: Final status
-- =====================================================

SELECT 'CONSTRAINTS FIXED - Menu and drink items should work now!' as status;
