"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import {
  Users,
  Calendar,
  MapPin,
  TrendingUp,
  Clock,
  CheckCircle,
  ArrowUpRight,
  MoreHorizontal,
  Plus,
  Activity
} from "lucide-react"
import { dashboardService } from "@/lib/database"
import type { DashboardStats } from "@/lib/types"

export default function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      const data = await dashboardService.getStats()
      setStats(data)
    } catch (error) {
      console.error("Error loading dashboard data:", error)
    } finally {
      setIsLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="p-8">
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[...Array(4)].map((_, i) => (
              <Card key={i} className="animate-pulse border-0 shadow-sm">
                <CardHeader className="pb-3">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </CardHeader>
                <CardContent>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-full"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 space-y-8">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 mb-1">
            Welcome back! 👋
          </h2>
          <p className="text-gray-600 text-sm">
            Here's what's happening with your Wine & Dine events today.
          </p>
        </div>
        <Button className="bg-amber-600 hover:bg-amber-700 text-white shadow-sm h-9">
          <Plus className="w-4 h-4 mr-2" />
          Create Event
        </Button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 bg-gradient-to-br from-blue-50 to-indigo-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-gray-700">Total Events</CardTitle>
            <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
              <Calendar className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats?.total_events || 0}</div>
            <div className="flex items-center mt-1">
              <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
              <p className="text-xs text-gray-600">
                {stats?.upcoming_events || 0} upcoming
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 bg-gradient-to-br from-purple-50 to-pink-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-gray-700">Total Bookings</CardTitle>
            <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
              <Users className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats?.total_tickets_sold || 0}</div>
            <div className="flex items-center mt-1">
              <Activity className="w-3 h-3 text-gray-400 mr-1" />
              <p className="text-xs text-gray-600">
                Tickets sold
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 bg-gradient-to-br from-green-50 to-emerald-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-gray-700">Total Revenue</CardTitle>
            <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              ${stats?.total_revenue?.toLocaleString() || '0'}
            </div>
            <div className="flex items-center mt-1">
              <ArrowUpRight className="w-3 h-3 text-green-500 mr-1" />
              <p className="text-xs text-gray-600">
                All time revenue
              </p>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm hover:shadow-md transition-shadow duration-200 bg-gradient-to-br from-orange-50 to-amber-50">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
            <CardTitle className="text-sm font-semibold text-gray-700">Active Venues</CardTitle>
            <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
              <MapPin className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">8</div>
            <div className="flex items-center mt-1">
              <Activity className="w-3 h-3 text-gray-400 mr-1" />
              <p className="text-xs text-gray-600">
                Across the city
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity and Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-sm bg-gradient-to-br from-slate-50 to-gray-50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
                <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
                  <Clock className="w-4 h-4 text-slate-600" />
                </div>
                <span>Recent Activity</span>
              </CardTitle>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreHorizontal className="w-4 h-4" />
              </Button>
            </div>
            <CardDescription className="text-sm text-gray-600">
              Latest updates from your events and bookings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {stats?.recent_bookings?.slice(0, 3).map((booking) => (
              <div key={booking.id} className="flex items-start space-x-3 p-3 rounded-lg bg-white/60 hover:bg-white/80 transition-colors backdrop-blur-sm">
                <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">New booking received</p>
                  <p className="text-xs text-gray-600 mt-1">
                    {booking.event?.name} - {booking.group_size} guests
                  </p>
                </div>
                <span className="text-xs text-gray-400 whitespace-nowrap">
                  {new Date(booking.created_at).toLocaleDateString()}
                </span>
              </div>
            )) || (
              <div className="text-center py-8">
                <Activity className="w-8 h-8 text-gray-300 mx-auto mb-2" />
                <p className="text-sm text-gray-500">No recent activity</p>
              </div>
            )}

            {stats?.recent_bookings && stats.recent_bookings.length > 0 && (
              <>
                <Separator className="my-4" />
                <Button variant="outline" className="w-full h-9 text-sm bg-white/60 hover:bg-white/80 backdrop-blur-sm" asChild>
                  <a href="/administrator/events">View All Bookings</a>
                </Button>
              </>
            )}
          </CardContent>
        </Card>

        <Card className="border-0 shadow-sm bg-gradient-to-br from-indigo-50 to-purple-50">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center space-x-2 text-lg font-semibold">
                <div className="w-8 h-8 bg-white/60 rounded-lg flex items-center justify-center backdrop-blur-sm">
                  <Users className="w-4 h-4 text-indigo-600" />
                </div>
                <span>Quick Actions</span>
              </CardTitle>
            </div>
            <CardDescription className="text-sm text-gray-600">
              Common administrative tasks
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button className="w-full justify-start h-10 bg-white/60 border-0 text-gray-700 hover:bg-white/80 backdrop-blur-sm shadow-sm" variant="outline" asChild>
              <a href="/administrator/events">
                <div className="w-6 h-6 bg-blue-100 rounded-md flex items-center justify-center mr-3">
                  <Calendar className="w-3 h-3 text-blue-600" />
                </div>
                Manage Events
              </a>
            </Button>
            <Button className="w-full justify-start h-10 bg-white/60 border-0 text-gray-700 hover:bg-white/80 backdrop-blur-sm shadow-sm" variant="outline" asChild>
              <a href="/administrator/venues">
                <div className="w-6 h-6 bg-orange-100 rounded-md flex items-center justify-center mr-3">
                  <MapPin className="w-3 h-3 text-orange-600" />
                </div>
                Manage Venues
              </a>
            </Button>
            <Button className="w-full justify-start h-10 bg-white/60 border-0 text-gray-700 hover:bg-white/80 backdrop-blur-sm shadow-sm" variant="outline" asChild>
              <a href="/administrator/artists">
                <div className="w-6 h-6 bg-purple-100 rounded-md flex items-center justify-center mr-3">
                  <Users className="w-3 h-3 text-purple-600" />
                </div>
                Manage Artists
              </a>
            </Button>
            <Button className="w-full justify-start h-10 bg-white/60 border-0 text-gray-700 hover:bg-white/80 backdrop-blur-sm shadow-sm" variant="outline" asChild>
              <a href="/administrator/sales">
                <div className="w-6 h-6 bg-green-100 rounded-md flex items-center justify-center mr-3">
                  <TrendingUp className="w-3 h-3 text-green-600" />
                </div>
                View Sales Reports
              </a>
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Success Message */}
      <Card className="border-0 bg-gradient-to-r from-green-50 to-emerald-50 shadow-sm">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-4">
            <div className="w-10 h-10 bg-white/60 rounded-lg flex items-center justify-center flex-shrink-0 backdrop-blur-sm">
              <CheckCircle className="w-5 h-5 text-green-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-green-900 mb-1">Wine & Dine Admin Portal Active!</h3>
              <p className="text-sm text-green-700 leading-relaxed">
                You are successfully logged in to the Wine & Dine administrator dashboard.
                All admin features are available to you.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}