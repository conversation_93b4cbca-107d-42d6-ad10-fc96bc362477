"use client"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Loader2, 
  Check,
  AlertCircle,
  Link
} from "lucide-react"
import { toast } from "sonner"
import { supabase } from "@/lib/supabaseClient"

interface MediaUploadProps {
  value?: string
  onChange: (url: string) => void
  label?: string
  accept?: string
  maxSize?: number // in MB
  className?: string
  placeholder?: string
  showUrlInput?: boolean
}

export default function MediaUpload({
  value = "",
  onChange,
  label = "Image",
  accept = "image/*",
  maxSize = 5,
  className = "",
  placeholder = "Upload an image or enter URL",
  showUrlInput = true
}: MediaUploadProps) {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)
  const [dragActive, setDragActive] = useState(false)
  const [urlInput, setUrlInput] = useState("")
  const [showUrlField, setShowUrlField] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const validateFile = (file: File): string | null => {
    if (!file.type.startsWith('image/')) {
      return "Please select an image file"
    }
    
    if (file.size > maxSize * 1024 * 1024) {
      return `File size must be less than ${maxSize}MB`
    }
    
    return null
  }

  const uploadFile = async (file: File): Promise<string> => {
    const fileExt = file.name.split('.').pop()
    const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
    const filePath = `${fileName}`

    try {
      const { data, error } = await supabase.storage
        .from('media')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        // If RLS policy error, provide helpful message
        if (error.message.includes('row-level security') || error.message.includes('policy')) {
          throw new Error("Image upload is not configured. Please use the URL input option instead.")
        }
        throw new Error(error.message)
      }

      // Get public URL
      const { data: { publicUrl } } = supabase.storage
        .from('media')
        .getPublicUrl(data.path)

      return publicUrl
    } catch (error) {
      // Re-throw with user-friendly message
      if (error instanceof Error) {
        throw error
      }
      throw new Error("Failed to upload image. Please try using a URL instead.")
    }
  }

  const handleFileSelect = async (file: File) => {
    const error = validateFile(file)
    if (error) {
      toast.error(error)
      return
    }

    setIsUploading(true)
    setUploadProgress(0)

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return prev
          }
          return prev + 10
        })
      }, 100)

      const url = await uploadFile(file)
      
      clearInterval(progressInterval)
      setUploadProgress(100)
      
      setTimeout(() => {
        onChange(url)
        setUploadProgress(0)
        toast.success("Image uploaded successfully")
      }, 500)

    } catch (error) {
      console.error("Upload error:", error)
      const errorMessage = error instanceof Error ? error.message : "Failed to upload image"
      toast.error(errorMessage)
      setUploadProgress(0)

      // If it's an RLS error, automatically show URL input
      if (errorMessage.includes('not configured') || errorMessage.includes('policy')) {
        setShowUrlField(true)
      }
    } finally {
      setIsUploading(false)
    }
  }

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
    
    const file = e.dataTransfer.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragActive(false)
  }

  const handleUrlSubmit = () => {
    if (urlInput.trim()) {
      try {
        new URL(urlInput) // Validate URL
        onChange(urlInput.trim())
        setUrlInput("")
        setShowUrlField(false)
        toast.success("Image URL added successfully")
      } catch {
        toast.error("Please enter a valid URL")
      }
    }
  }

  const removeImage = () => {
    onChange("")
    setUrlInput("")
    if (fileInputRef.current) {
      fileInputRef.current.value = ""
    }
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {label && <Label className="text-sm font-medium">{label}</Label>}
      
      {/* Current Image Preview */}
      {value && (
        <Card className="relative">
          <CardContent className="p-3">
            <div className="flex items-center space-x-3">
              <img
                src={value}
                alt="Preview"
                className="w-16 h-16 object-cover rounded-lg"
                onError={(e) => {
                  e.currentTarget.style.display = 'none'
                }}
              />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  Image uploaded
                </p>
                <p className="text-xs text-gray-500 truncate">
                  {value}
                </p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={removeImage}
                className="text-red-600 hover:text-red-700 hover:bg-red-50"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Area */}
      {!value && (
        <div
          className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
            dragActive 
              ? 'border-amber-500 bg-amber-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          {isUploading ? (
            <div className="space-y-3">
              <Loader2 className="mx-auto h-8 w-8 text-amber-600 animate-spin" />
              <div className="space-y-2">
                <p className="text-sm text-gray-600">Uploading image...</p>
                <Progress value={uploadProgress} className="w-full max-w-xs mx-auto" />
                <p className="text-xs text-gray-500">{uploadProgress}%</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              <ImageIcon className="mx-auto h-8 w-8 text-gray-400" />
              <div className="space-y-1">
                <p className="text-sm text-gray-600">{placeholder}</p>
                <p className="text-xs text-gray-500">
                  Drag and drop or click to browse (max {maxSize}MB)
                </p>
              </div>
              <div className="flex items-center justify-center space-x-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                  disabled={isUploading}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Browse Files
                </Button>
                {showUrlInput && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowUrlField(!showUrlField)}
                    disabled={isUploading}
                  >
                    <Link className="w-4 h-4 mr-2" />
                    Add URL
                  </Button>
                )}
              </div>
            </div>
          )}
        </div>
      )}

      {/* URL Input Field */}
      {showUrlField && !value && (
        <div className="flex space-x-2">
          <Input
            value={urlInput}
            onChange={(e) => setUrlInput(e.target.value)}
            placeholder="https://example.com/image.jpg"
            onKeyPress={(e) => e.key === 'Enter' && handleUrlSubmit()}
          />
          <Button
            type="button"
            onClick={handleUrlSubmit}
            disabled={!urlInput.trim()}
            size="sm"
          >
            <Check className="w-4 h-4" />
          </Button>
          <Button
            type="button"
            variant="ghost"
            onClick={() => {
              setShowUrlField(false)
              setUrlInput("")
            }}
            size="sm"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>
      )}

      {/* Hidden File Input */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Help Text */}
      <p className="text-xs text-gray-500">
        Supported formats: JPG, PNG, GIF, WebP. Maximum size: {maxSize}MB
        {showUrlInput && (
          <span className="block mt-1 text-amber-600">
            💡 If upload fails, you can use the "Add URL" option to link to images hosted elsewhere.
          </span>
        )}
      </p>
    </div>
  )
}
